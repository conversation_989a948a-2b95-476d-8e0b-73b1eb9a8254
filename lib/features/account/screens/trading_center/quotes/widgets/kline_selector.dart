import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/domain/models/kline_option.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/kline/kline_selector_bar.dart';

class KlineSelector extends StatelessWidget {
  const KlineSelector({
    super.key,
    required this.instrument,
  });

  final String instrument;

  @override
  Widget build(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, (DataStatus, KlineOption?)>(
      selector: (state) => (state.klineDetailListStatus, state.klineOption),
      builder: (context, state) {
        final selectedOption = state.$2;

        return KlineSelectorBar(
          primaryOptions: KlineConstants.options,
          secondaryOptions: KlineConstants.subOptions,
          selectedPrimary: selectedOption,
          selectedSecondary: KlineConstants.subOptions.any((e) => e.id == selectedOption?.id)
              ? selectedOption
              : KlineConstants.subOptions[0],
          status: state.$1,
          onPrimaryTap: (option) => context.read<TradingCubit>().getKlineDetailList(instrument, option),
          onSecondaryChanged: (option) => context.read<TradingCubit>().getKlineDetailList(instrument, option),
        );
      },
    );
  }
}
