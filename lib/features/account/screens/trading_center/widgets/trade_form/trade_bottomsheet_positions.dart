import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/kline_option.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:k_chart_plus/chart_style.dart';
import 'package:k_chart_plus/entity/k_line_entity.dart';
import 'package:k_chart_plus/k_chart_widget.dart';
import 'package:k_chart_plus/renderer/main_renderer.dart';
import 'package:k_chart_plus/utils/data_util.dart';

class TradeBottomsheetPositions extends StatelessWidget {
  const TradeBottomsheetPositions({super.key, required this.data});

  final OrderRecord data;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 10.gw, horizontal: 12.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.gw),
          topRight: Radius.circular(10.gw),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        spacing: 8,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            spacing: 5.gw,
            children: [
              SymbolChip(
                name: MarketSymbol.getMarketType(data.market) ?? '',
                chipColor: context.theme.primaryColor,
              ),
              Expanded(child: Text(data.symbolName ?? '', style: context.textTheme.regular)),
              Text(
                '(${data.symbol ?? ''})',
                style: context.textTheme.regular.fs13.w300,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 12,
            children: [
              Expanded(
                child: AmountRow(
                    title: 'available'.tr(), value: '${data.restNum?.toStringAsFixed(2) ?? 0.00}', fontSize: 13.gr),
              ),
              Expanded(
                child: AmountRow(
                  title: 'averagePrice'.tr(),
                  value: '${data.buyAvgPrice?.toStringAsFixed(2) ?? 0.00}',
                  fontSize: 13.gr,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 12,
            children: [
              Expanded(
                child: AmountRow(
                    title: 'total'.tr(), value: '${data.buyTotalNum?.toStringAsFixed(2) ?? 0.00}', fontSize: 13.gr),
              ),
              Expanded(
                child: AmountRow(
                    title: 'currentPrice'.tr(),
                    value: '${data.stockPrice?.toStringAsFixed(2) ?? 0.00}',
                    fontSize: 13.gr),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 12,
            children: [
              Expanded(
                child: AmountRow(
                  title: 'floatingProfitLoss'.tr(),
                  value: '${data.floatingProfitLoss?.toStringAsFixed(2)}',
                  fontSize: 13.gr,
                ),
              ),
              Expanded(
                child: AmountRow(
                    title: 'marketValue'.tr(),
                    value: '${data.marketValue?.toStringAsFixed(2) ?? 0.00}',
                    fontSize: 13.gr),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 12,
            children: [
              Expanded(
                flex: 2,
                child: AmountRow(
                  title: 'plRatio'.tr(),
                  value: '${data.floatingProfitLossRate?.toStringAsFixed(2)}%',
                  fontSize: 13.gr,
                ),
              ),
              Spacer(),
            ],
          ),
          AmountRow(
            title: 'holding'.tr(),
            value: '${data.createTime}',
            fontSize: 13.gr,
          ),
          Row(
            spacing: 8,
            children: [
              Expanded(
                flex: 2,
                child: SizedBox(
                  height: 0.27.gsh,
                  child: AbsorbPointer(
                    child: BlocSelector<TradingCubit, TradingState, (DataStatus, StockKlineResponse?, KlineOption?)>(
                      selector: (state) => (state.klineDetailListStatus, state.klineDetailList, state.klineOption),
                      builder: (context, state) {
                        if (state.$1 == DataStatus.loading && state.$2?.data?.list == null) {
                          return ShimmerWidget();
                        }
                        if (state.$1 == DataStatus.failed || state.$2?.data?.list == null) {
                          return Center(
                            child: Icon(Icons.error),
                          );
                        }
                        final isLine = state.$3?.type == "timeLine";
                        final scaleX = switch (state.$3?.id) {
                          "weekly-kline" => 0.5,
                          "monthly-kline" => 0.5,
                          "yearly-kline" => 1.0,
                          "intraday" => 0.15,
                          "5day" => 0.03,
                          _ => 0.8,
                        };

                        return Padding(
                          padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
                          child: KChartWidget(
                            _processData(state.$2?.data?.list, isLine),
                            ChartStyle(),
                            ChartColors(
                              upColor: const Color(0xFFD2544F),
                              dnColor: const Color(0xFF5DAF78),
                              gridColor: Colors.transparent,
                              bgColor: context.theme.cardColor,
                              ma5Color: const Color(0xffE5B767),
                              ma10Color: const Color(0xff1FD1AC),
                              ma30Color: const Color(0xffB48CE3),
                              ma60Color: const Color(0xFFD5405D),
                            ),
                            getColorCallback: (value) => value.getValueColor(context),
                            mBaseHeight: 0.27.gsh,
                            isTrendLine: false,
                            scaleX: scaleX,
                            mainState: MainState.MA,
                            volHidden: true,
                            isTapShowInfoDialog: true,
                            showInfoDialog: false,
                            secondaryStateLi: {},
                            timeFormat: TimeFormat.YEAR_MONTH_DAY_WITH_HOUR,
                            verticalTextAlignment: VerticalTextAlignment.right,
                            isLine: isLine,
                            xFrontPadding: 0,
                            locale: context.locale.languageCode,
                            maDayList: const [5, 10, 20, 30, 60],
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              Theme(
                data: Theme.of(context).copyWith(
                    textButtonTheme: TextButtonThemeData(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 4),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    alignment: Alignment.centerLeft,
                    foregroundColor: context.theme.primaryColor,
                    iconColor: context.theme.primaryColor,
                    textStyle: context.textTheme.regular.copyWith(color: context.theme.primaryColor),
                  ),
                )),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        context.read<TradingCubit>().setTradeType(TradeTabType.Quotes);
                        if (data.isIndex) {
                          getIt<NavigatorService>().pushReplace(
                            AppRouter.routeTradingCenter,
                            arguments: TradingArguments(
                              instrumentInfo: Instrument(
                                instrument: data.instrument,
                              ),
                              selectedIndex: TradeTabType.Quotes.index,
                              isIndexTrading: data.isIndex,
                            ),
                          );
                        } else {
                          Navigator.pop(context);
                        }
                      },
                      label: Text(
                        'quote'.tr(),
                      ),
                      icon: Icon(LucideIcons.chart_no_axes_combined),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        context.read<TradingCubit>().toggleTradeDirection(TradeDirection.buy);
                        Navigator.pop(context);
                      },
                      label: Text(
                        'buy'.tr(),
                      ),
                      icon: Icon(LucideIcons.shopping_cart),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        context.read<TradingCubit>().toggleTradeDirection(TradeDirection.sell);
                        if (data.isIndex) {
                          context.read<TradingCubit>().setSelectedPositionSellLong(data);
                        }
                        Navigator.pop(context);
                      },
                      label: Text(
                        'sell'.tr(),
                      ),
                      icon: Icon(LucideIcons.banknote),
                    ),
                    // TextButton.icon(
                    //   onPressed: () {
                    //     context.read<TradingCubit>().toggleTradeDirection(TradeDirection.sell);
                    //     Navigator.pop(context);
                    //   },
                    //   label: Text(
                    //     'closePosition'.tr(),
                    //   ),
                    //   icon: Icon(LucideIcons.chart_no_axes_column),
                    // )
                  ],
                ),
              )
            ],
          ),
          SizedBox(height: 10.gw),
        ],
      ),
    );
  }

  List<KLineEntity> _processData(List<KlineItem>? list, bool isLine) {
    if (list == null) return [];

    List<KLineEntity> klineEntities = [];
    double? previousPrice;

    for (var item in list) {
      final openPrice = previousPrice ?? (isLine ? item.price ?? 0 : item.open ?? 0);
      final klineEntity = KLineEntity.fromCustom(
        time: item.time != null ? item.time! * 1000 : 0,
        close: isLine ? item.price ?? 0 : item.close ?? 0,
        open: openPrice,
        high: isLine ? item.price ?? 0 : item.high ?? 0,
        low: isLine ? item.price ?? 0 : item.low ?? 0,
        vol: item.volume ?? 0,
        amount: item.price ?? 0,
      );

      klineEntities.add(klineEntity);
      previousPrice = item.price ?? 0;
    }

    DataUtil.calculate(klineEntities);
    return klineEntities;
  }
}
