import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_future_sltp.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_orders.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_dialog_add_margin.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_all_info_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'list_view_cell/account_entrust_cell.dart';
import 'list_view_cell/account_position_cell.dart';
import 'list_view_cell/account_trade_cell.dart';

class AccountOrderListView extends StatefulWidget {
  final TradingAccountType tradingAccountType;
  final MarketCategory marketCategory;
  final ContractSummaryPageRecord? contractModel; // 合约类型必传，用于点击跳转至交易时传值
  final OrderType orderType;
  final OrderListState orderListState;
  final Function(bool isLoadMore) onFetch;

  const AccountOrderListView({
    super.key,
    required this.tradingAccountType,
    required this.marketCategory,
    this.contractModel,
    required this.orderType,
    required this.orderListState,
    required this.onFetch,
  });

  @override
  State<StatefulWidget> createState() => _AccountOrderListViewState();
}

class _AccountOrderListViewState extends State<AccountOrderListView> {
  // with AutomaticKeepAliveClientMixin {
  // @override
  // bool get wantKeepAlive => true;

  final RefreshController _refreshController = RefreshController();

  late OrderListState model = widget.orderListState;

  @override
  void initState() {
    super.initState();
    // 初始化时请求第一次数据
    widget.onFetch(false);
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AccountOrderListView oldWidget) {
    if (widget.orderListState != oldWidget.orderListState) {
      setState(() {
        model = widget.orderListState;
      });
    }
    super.didUpdateWidget(oldWidget);
  }

  void _onRefresh() async {
    await widget.onFetch.call(false);
    _refreshController
      ..resetNoData()
      ..refreshCompleted();
  }

  void _onLoadMore() async {
    /// 过滤首页为空时加载更多
    if (model.records.isNotEmpty) {
      await widget.onFetch.call(true);
    }
    if (model.hasMoreData) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TradingCubit(),
      child: BlocListener<TradingCubit, TradingState>(
        listener: (context, state) {
          if (state.orderCancelStatus == DataStatus.success || state.orderCancelStatus == DataStatus.failed) {
            GPEasyLoading.dismiss();
            if (state.orderCancelStatus == DataStatus.success) {
              GPEasyLoading.showSuccess(message: 'orderCancelSuccess'.tr());
              widget.onFetch(false);
            } else if (state.orderCancelStatus == DataStatus.failed) {
              GPEasyLoading.showToast(state.error);
            }
          }
        },
        child: CommonRefresher(
          controller: _refreshController,
          enablePullDown: false,
          enablePullUp: true,
          onRefresh: () => _onRefresh(),
          onLoading: () => _onLoadMore(),
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // 使用 SliverOverlapInjector 注入由外层 NestedScrollView 的 SliverOverlapAbsorber 预留的重叠区域，
              // 解决外层可折叠头部与内层 CustomScrollView(slivers) 的可滚动内容之间的重叠问题。
              // 外层实现要求：
              // 1) 用 NestedScrollView 包裹该列表；
              // 2) 在 NestedScrollView 的 headerSliverBuilder 中放置 SliverOverlapAbsorber，
              //    且 handle 使用同一个 NestedScrollView.sliverOverlapAbsorberHandleFor(context)；
              // 3) 内层 body 的 slivers 列表顶部加入本 SliverOverlapInjector（当前这段），以抵消重叠。
              SliverOverlapInjector(
                handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
              ),
              if (model.records.isEmpty) ...[
                if (model.isInitialLoad && model.status == DataStatus.loading) ...[
                  ///骨架图
                  SliverToBoxAdapter(child: _buildShimmerWidget(widget.orderType)),
                ] else ...[
                  /// 空视图
                  SliverFillRemaining(
                      child: TableEmptyWidget(
                    height: 40,
                    width: 40,
                    title: switch (widget.orderType) {
                      OrderType.positions => "no_holdings".tr(),
                      OrderType.trades => "no_trades".tr(),
                      OrderType.order => "no_entrusts".tr(),
                    },
                    margin: EdgeInsets.symmetric(horizontal: 18.gw),
                    radius: 10.gw,
                  )),
                ]
              ],
              if (model.records.isNotEmpty)
                SliverPadding(
                  padding: EdgeInsets.symmetric(horizontal: 18.gw),
                  sliver: SliverList.separated(
                    itemCount: model.records.length,
                    separatorBuilder: (_, __) =>
                        widget.orderType == OrderType.positions ? 10.verticalSpace : const SizedBox.shrink(),
                    itemBuilder: (context, index) {
                      final item = model.records[index];
                      return _buildCell(
                        context,
                        category: widget.marketCategory,
                        item: item,
                        index: index,
                        isIndexTrading: item.isIndex,
                      );
                    },
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCell(
    BuildContext context, {
    required MarketCategory category,
    required FTradeAcctOrderRecords item,
    required int index,
    required bool isIndexTrading,
  }) {
    final cubit = context.read<AccountScreenCubitV2>();
    final tradeType = TradeTypeOption.fromValue(item.tradeType);
    final tradeColor = tradeType.color(context);
    return switch (widget.orderType) {
      OrderType.positions => AccountPositionCell(
          marketCategory: category,
          data: item,
          onTap: () async {
            /// 期货
            if (category == MarketCategory.cnFutures) {
              await getIt<NavigatorService>().push(
                AppRouter.routeFTradeAllInfo,
                arguments: (
                  CNFuturesMarketType.fromMarketCode(item.market).idx,
                  FTradeAllInfoTitlesType.quotation,
                  item.toFTradeListItemModel()
                ),
              ).then((value) {
                if (!context.mounted) return;
                cubit.fetchSpotScreenCurrentData();
              });
              return;
            }

            /// 除期货
            ///
            ///
            // if (isIndexTrading) {
            //   context.read<MainCubit>().selectedNavigationItem(NavigationItem.trade);
            //   // Switch to Index tab inside market screenm
            //   context.read<MarketCubit>().updateMainHeaderTab(1);
            //   context.read<IndexTradeCubit>().updateSelectedIndex(
            //         context
            //             .read<IndexTradeCubit>()
            //             .state
            //             .indexes
            //             .indexWhere((element) => element.instrument == item.instrument),
            //         animate: true,
            //       );
            //   getIt<NavigatorService>().popToRoot();
            // } else {
            await getIt<NavigatorService>()
                .push(
              AppRouter.routeTradingCenter,
              arguments: TradingArguments(
                instrumentInfo: Instrument(
                  instrument: item.instrument,
                ),
                selectedIndex: TradeTabType.Quotes.index,
                contract: switch (widget.tradingAccountType) {
                  TradingAccountType.Contract => ContractSummaryData.fromRecord(widget.contractModel!),
                  _ => null,
                },
                tradeType: switch (widget.tradingAccountType) {
                  TradingAccountType.Spot => 'spotTrading'.tr(),
                  _ => null,
                },
                isIndexTrading: item.isIndex,
              ),
            )
                .then((value) {
              if (!context.mounted) return;
              cubit.fetchSpotScreenCurrentData();
            });
            // }
          },
          onTapTpSL: () {
            TradeBottomSheetFutureSlTp(
                context: context,
                contractName: item.symbolName,
                tradeType: tradeType,
                directionText: tradeType.shortText,
                directionColor: tradeColor,
                openPrice: item.buyAvgPrice,
                currentPrice: item.stockPrice,
                currency: item.currency,
                restNum: item.restNum,
                initialTakeProfitValue: item.takeProfitValue,
                initialStopLossValue: item.stopLossValue,
                onPressedOk: (takeProfitValue, stopLossValue) async {
                  if (tradeType == TradeTypeOption.openLong) {
                    if (takeProfitValue != 0 && takeProfitValue <= item.buyAvgPrice) {
                      return GPEasyLoading.showToast("take_profit_above_entry".tr()); // 止盈价格必须高于买入价格
                    } else if (stopLossValue != 0 && stopLossValue >= item.buyAvgPrice) {
                      return GPEasyLoading.showToast("stop_loss_below_entry".tr()); // 止损价格必须低于买入价格
                    }
                  } else {
                    if (takeProfitValue != 0 && takeProfitValue >= item.buyAvgPrice) {
                      return GPEasyLoading.showToast("take_profit_below_entry".tr()); // 止盈价格必须低于开仓价格
                    } else if (stopLossValue != 0 && stopLossValue <= item.buyAvgPrice) {
                      return GPEasyLoading.showToast("stop_loss_above_entry".tr()); // 止损价格必须高于开仓价格
                    }
                  }

                  final flag = await cubit.setFuturesStopLine(
                      positionId: item.id, takeProfitValue: takeProfitValue, stopLossValue: stopLossValue);
                  if (flag) {
                    GPEasyLoading.showToast("success".tr());
                    await Future.delayed(const Duration(milliseconds: 500));
                    if (context.mounted) Navigator.of(context).pop();
                  }
                }).show();
          },
          onTapAdd: () async {
            await TradeAddMarginDialog(
              context,
              data: item,
              availableMarginStream: cubit.stream.map((state) {
                /// FIXME 待验证
                // 从当前市场分类的订单列表中找到对应的item
                final currentViewModel =
                    state.spotViewModels.isEmpty ? null : state.spotViewModels[state.spotScreenCurrentIndex];
                if (currentViewModel == null) return item.availableMargin;

                final orderListState = currentViewModel.details[widget.orderType];
                if (orderListState == null) return item.availableMargin;

                // 根据ID找到对应的订单记录
                final updatedItem = orderListState.records.where((record) => record.id == item.id).firstOrNull;

                return updatedItem?.availableMargin ?? item.availableMargin;
              }).distinct(), // 避免重复的值
              onPressedSubmit: (amount) async {
                final flag = await cubit.addMargin(positionId: item.id, amount: amount);
                if (flag) {
                  GPEasyLoading.showToast("success".tr());
                  await Future.delayed(const Duration(milliseconds: 500));
                  if (context.mounted) Navigator.of(context).pop();
                }
              },
            ).show();
          },
          onTapDetail: () {
            // AccountContractDetailCubit
            getIt<NavigatorService>().push(AppRouter.routeSpotPositionDetail, arguments: {
              'id': item.id,
            });
          },
        ),
      OrderType.trades => AccountTradeCell(
          data: item,
          isLast: index == model.records.length - 1,
          onTap: () {
            context.read<TradingCubit>().getKlineDetailList(item.instrument, KlineConstants.options[0]);
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              builder: (_) {
                return MultiBlocProvider(
                  providers: [
                    BlocProvider.value(
                      value: context.read<TradingCubit>(),
                    ),
                    BlocProvider(
                      create: (_) => FTradeKLineCubit(FTradeKLineScrollRepository()),
                    ),
                  ],
                  child: Builder(builder: (innerContext) {
                    if (item.isCnFTrade) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        innerContext
                            .read<FTradeKLineCubit>()
                            .fetchTimeLineSubData(instrument: item.instrument, period: 'day');
                      });
                    }
                    return TradeBottomsheetOrders(data: item.toOrderRecord(), isTradeDetails: true, isTrading: false);
                  }),
                );
              },
            );
          },
        ),
      OrderType.order => AccountEntrustCell(
          data: item,
          isLast: index == model.records.length - 1,
          onTap: () {
            context.read<TradingCubit>().getKlineDetailList(item.instrument, KlineConstants.options[0]);
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              builder: (_) {
                return MultiBlocProvider(
                  providers: [
                    BlocProvider.value(
                      value: context.read<TradingCubit>(),
                    ),
                    BlocProvider(
                      create: (_) => FTradeKLineCubit(FTradeKLineScrollRepository()),
                    ),
                  ],
                  child: Builder(builder: (innerContext) {
                    if (item.isCnFTrade) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        innerContext
                            .read<FTradeKLineCubit>()
                            .fetchTimeLineSubData(instrument: item.instrument, period: 'day');
                      });
                    }
                    return TradeBottomsheetOrders(data: item.toOrderRecord());
                  }),
                );
              },
            );
          },
          onTapCancelBtn: () {
            showDialog(
              context: context,
              builder: (_) => BlocProvider.value(
                value: context.read<TradingCubit>(),
                child: CancelOrderDialog(orderId: item.id),
              ),
            );
          },
        ),
    };
  }

  Widget _buildShimmerWidget(OrderType type) {
    // 生成 3 个条目索引
    return Column(
      children: List.generate(3, (_) {
        return switch (type) {
          OrderType.positions => AccountPositionShimmerCell(marketCategory: widget.marketCategory),
          OrderType.trades => AccountTradeShimmerCell(),
          OrderType.order => AccountEntrustShimmerCell(),
        };
      }),
    );
  }
}
