import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

/// 自选股状态管理类
/// Watch list state management class
class WatchListState extends Equatable {
  /// 添加到自选股的状态
  /// Status of adding to watch list
  final DataStatus addToWatchListStatus;

  /// 从自选股移除的状态
  /// Status of removing from watch list
  final DataStatus removeFromWatchListStatus;

  /// 获取自选股列表的状态
  /// Status of getting watch list
  final DataStatus getWatchListStatus;

  /// 获取自选股详情的状态
  /// Status of getting watch list detail
  final DataStatus getWatchListDetailStatus;

  /// 根据标的获取自选股的状态
  /// Status of getting watch list by instrument
  final DataStatus getWatchListByInstrumentStatus;

  /// 错误信息
  /// Error message
  final String? error;

  /// 自选股列表
  /// Watch list items
  final List<WatchlistItemEntity>? watchList;

  /// 自选股详情
  /// Watch list detail
  final WatchlistItemEntity? watchListDetail;

  /// 根据标的查询的自选股项
  /// Watch list item by instrument
  final WatchlistItemEntity? watchListByInstrument;

  /// 当前页码
  /// Current page number
  final int currentPage;

  /// 总页数
  /// Total pages
  final int totalPages;

  /// 是否还有更多数据
  /// Whether there are more data
  final bool hasMore;

  /// 是否正在加载更多
  /// Whether loading more data
  final bool isLoadMore;

  /// 价格排序类型
  /// Sort type by price
  final SortType? sortByPriceAsc;

  /// 涨跌幅排序类型
  /// Sort type by change
  final SortType? sortByChangeAsc;

  final String? sortField;

  final String? market;

  /// 构造函数
  /// Constructor
  const WatchListState({
    this.addToWatchListStatus = DataStatus.idle,
    this.removeFromWatchListStatus = DataStatus.idle,
    this.getWatchListStatus = DataStatus.idle,
    this.getWatchListDetailStatus = DataStatus.idle,
    this.getWatchListByInstrumentStatus = DataStatus.idle,
    this.error,
    this.watchList,
    this.watchListDetail,
    this.watchListByInstrument,
    this.currentPage = 1,
    this.totalPages = 1,
    this.hasMore = true,
    this.isLoadMore = false,
    this.sortByPriceAsc,
    this.sortByChangeAsc,
    this.sortField,
    this.market,
  });

  /// 创建状态副本并更新指定字段
  /// Create a copy of the state with updated fields
  WatchListState copyWith({
    DataStatus? addToWatchListStatus,
    DataStatus? removeFromWatchListStatus,
    DataStatus? getWatchListStatus,
    DataStatus? getWatchListDetailStatus,
    DataStatus? getWatchListByInstrumentStatus,
    String? error,
    List<WatchlistItemEntity>? watchList,
    WatchlistItemEntity? watchListDetail,
    ValueGetter<WatchlistItemEntity?>? watchListByInstrument,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
    bool? isLoadMore,
    SortType? Function()? sortByPriceAsc,
    SortType? Function()? sortByChangeAsc,
    String? Function()? sortField,
    String? Function()? market,
  }) {
    return WatchListState(
      addToWatchListStatus: addToWatchListStatus ?? this.addToWatchListStatus,
      removeFromWatchListStatus: removeFromWatchListStatus ?? this.removeFromWatchListStatus,
      getWatchListStatus: getWatchListStatus ?? this.getWatchListStatus,
      getWatchListDetailStatus: getWatchListDetailStatus ?? this.getWatchListDetailStatus,
      getWatchListByInstrumentStatus: getWatchListByInstrumentStatus ?? this.getWatchListByInstrumentStatus,
      error: error ?? this.error,
      watchList: watchList ?? this.watchList,
      watchListDetail: watchListDetail ?? this.watchListDetail,
      watchListByInstrument: watchListByInstrument != null ? watchListByInstrument() : this.watchListByInstrument,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
      isLoadMore: isLoadMore ?? this.isLoadMore,
      sortByPriceAsc: sortByPriceAsc != null ? sortByPriceAsc() : this.sortByPriceAsc,
      sortByChangeAsc: sortByChangeAsc != null ? sortByChangeAsc() : this.sortByChangeAsc,
      sortField: sortField != null ? sortField() : this.sortField,
      market: market != null ? market() : this.market,
    );
  }

  @override
  List<Object?> get props => [
        addToWatchListStatus,
        removeFromWatchListStatus,
        getWatchListStatus,
        getWatchListDetailStatus,
        getWatchListByInstrumentStatus,
        error,
        watchList,
        watchListDetail,
        watchListByInstrument,
        currentPage,
        totalPages,
        hasMore,
        isLoadMore,
        sortByPriceAsc,
        sortByChangeAsc,
        sortField,
        market,
      ];

  @override
  String toString() {
    return 'WatchListState('
        'addToWatchListStatus: $addToWatchListStatus, '
        'removeFromWatchListStatus: $removeFromWatchListStatus, '
        'getWatchListStatus: $getWatchListStatus, '
        'getWatchListDetailStatus: $getWatchListDetailStatus, '
        'getWatchListByInstrumentStatus: $getWatchListByInstrumentStatus, '
        'error: $error, '
        'watchList: ${watchList?.length}, '
        'watchListDetail: $watchListDetail, '
        'watchListByInstrument: $watchListByInstrument, '
        'currentPage: $currentPage, '
        'totalPages: $totalPages, '
        'hasMore: $hasMore, '
        'isLoadMore: $isLoadMore, '
        'sortByPriceAsc: $sortByPriceAsc, '
        'sortByChangeAsc: $sortByChangeAsc, '
        'sortField: $sortField, '
        'market: $market  '
        ')';
  }
}
