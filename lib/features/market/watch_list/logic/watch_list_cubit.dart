import 'dart:async';

import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/watchlist.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/services/polling/polling_sevice_v2.dart';
import 'package:injectable/injectable.dart';

import 'watch_list_state.dart';

@singleton
class WatchListCubit extends AuthAwareCubit<WatchListState> {
  WatchListCubit() : super(const WatchListState());

  @override
  void onLoggedIn() => getWatchList();

  @override
  void onLoggedOut() => emit(const WatchListState());

  Future<void> startPolling({bool isFromHome = false}) async {
    getIt<PollingServiceV2>().startPolling(
      id: kGPWatchListDataPolling,
      onPoll: () async {
        await getWatchList(isPolling: true, isFromHome: isFromHome);
        return true;
      },
      shouldStop: () => !getIt<UserCubit>().state.isLogin,
    );
  }

  Future<void> stopPolling() async {
    getIt<PollingServiceV2>().stopPolling(kGPWatchListDataPolling);
  }

  Future<void> addToWatchList({
    required String symbol,
    required String market,
    int putSort = 0,
    required String securityType,
    int sort = 0,
  }) async {
    emit(state.copyWith(addToWatchListStatus: DataStatus.loading, watchListByInstrument: null));
    try {
      final flag = await WatchlistApi.addToWatchList(
        symbol: symbol,
        market: market,
        putSort: putSort,
        securityType: securityType,
        sort: sort,
      );
      if (flag) {
        emit(state.copyWith(addToWatchListStatus: DataStatus.success, watchListByInstrument: null));
      } else {
        emit(state.copyWith(addToWatchListStatus: DataStatus.failed, watchListByInstrument: null));
      }
    } catch (e) {
      emit(state.copyWith(addToWatchListStatus: DataStatus.failed, watchListByInstrument: null));
    }
  }

  Future<void> removeFromWatchList(int choiceId) async {
    final watchModel = state.watchListByInstrument;
    emit(state.copyWith(removeFromWatchListStatus: DataStatus.loading, watchListByInstrument: null));
    try {
      final flag = await WatchlistApi.removeFromWatchList(choiceId);
      if (flag) {
        emit(state.copyWith(removeFromWatchListStatus: DataStatus.success, watchListByInstrument: null));
      } else {
        emit(state.copyWith(
          removeFromWatchListStatus: DataStatus.failed,
          watchListByInstrument: () => watchModel,
        ));
      }
    } catch (e) {
      emit(state.copyWith(removeFromWatchListStatus: DataStatus.failed, watchListByInstrument: () => watchModel));
    }
  }

  Future<void> getWatchList(
      {bool loadMore = false, bool isPolling = false, bool skipLoading = false, bool isFromHome = false}) async {
    if (loadMore && !state.hasMore) return;
    if (state.getWatchListStatus.isLoading) return;
    if (!isPolling && !skipLoading) {
      emit(
        state.copyWith(
          getWatchListStatus: DataStatus.loading,
          currentPage: loadMore ? state.currentPage + 1 : 1,
          isLoadMore: loadMore,
        ),
      );
    }
    try {
      final result = await WatchlistApi.getWatchList(
        pageNumber: state.currentPage,
        pageSize: 20,
        market: isFromHome ? null : state.market,
        field: state.sortField,
        order: _order(),
      );

      if (result != null) {
        final newWatchList = loadMore ? [...?state.watchList, ...result.records] : result.records;

        emit(state.copyWith(
          getWatchListStatus: DataStatus.success,
          watchList: newWatchList,
          hasMore: (newWatchList.length) < (result.total),
          totalPages: result.total,
          currentPage: result.current,
        ));
      } else {
        emit(state.copyWith(
          getWatchListStatus: DataStatus.failed,
        ));
      }
    } catch (e) {
      emit(state.copyWith(getWatchListStatus: DataStatus.failed));
    }
  }

  Future<void> getWatchListDetail(String choiceId) async {
    emit(state.copyWith(getWatchListDetailStatus: DataStatus.loading));
    try {
      final result = await WatchlistApi.getWatchListDetail(choiceId);
      if (result != null) {
        emit(state.copyWith(
          getWatchListDetailStatus: DataStatus.success,
          watchListDetail: result,
        ));
      } else {
        emit(state.copyWith(
          getWatchListDetailStatus: DataStatus.failed,
        ));
      }
    } catch (e) {
      emit(state.copyWith(getWatchListDetailStatus: DataStatus.failed));
    }
  }

  Future<void> getWatchListByInstrument(String instrument) async {
    emit(state.copyWith(getWatchListByInstrumentStatus: DataStatus.loading));
    final result = await WatchlistApi.getWatchlistByInstrument(instrument);
    emit(state.copyWith(
      getWatchListByInstrumentStatus: result != null ? DataStatus.success : DataStatus.failed,
      watchListByInstrument: () => result,
    ));
  }

  void handleSortByPrice() {
    final nextSortType = state.sortByPriceAsc == null
        ? SortType.ASC
        : state.sortByPriceAsc == SortType.ASC
            ? SortType.DESC
            : null;
    emit(state.copyWith(
      sortByPriceAsc: () => nextSortType,
      sortField: () => nextSortType == null ? null : 'latestPrice',
      sortByChangeAsc: () => null,
    ));
    getWatchList(skipLoading: true);
  }

  void handleSortByChange() {
    final nextSortType = state.sortByChangeAsc == null
        ? SortType.ASC
        : state.sortByChangeAsc == SortType.ASC
            ? SortType.DESC
            : null;
    emit(state.copyWith(
      sortByChangeAsc: () => nextSortType,
      sortField: () => nextSortType == null ? null : 'gain',
      sortByPriceAsc: () => null,
    ));
    getWatchList(skipLoading: true);
  }

  String _order() {
    if (state.sortField == 'latestPrice') {
      return state.sortByPriceAsc == SortType.ASC ? 'asc' : 'desc';
    } else {
      return state.sortByChangeAsc == SortType.ASC ? 'asc' : 'desc';
    }
  }

  void setMarket(String? market) {
    emit(state.copyWith(market: () => market));
    getWatchList(skipLoading: true);
  }
}
