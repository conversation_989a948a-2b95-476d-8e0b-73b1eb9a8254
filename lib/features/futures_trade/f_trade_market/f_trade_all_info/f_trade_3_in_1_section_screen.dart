import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/f_trade_k_line_scroll_view.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_profile/logic/t_trade_profile_scroll_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_profile/t_trade_profile_scroll_view.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';

/// 行情 简况 咨询 3和1 页面
class FTrade3In1SectionScreen extends StatefulWidget {
  final CNFuturesMarketType type;
  final FTradeListItemModel data;
  final void Function(int) onChangeAllInfoScreenTitlesAction;
  const FTrade3In1SectionScreen({
    super.key,
    required this.type,
    required this.data,
    required this.onChangeAllInfoScreenTitlesAction,
  });

  @override
  State<FTrade3In1SectionScreen> createState() => _FTrade3In1SectionScreenState();
}

class _FTrade3In1SectionScreenState extends State<FTrade3In1SectionScreen> {
  final titles = ['tabMarket', 'tabProfile'];
  int _currentTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    final tabData = titles.map((title) => title.tr()).toList();

    return Column(
      children: [
        Container(
          height: 40.gw,
          decoration: BoxDecoration(color: context.theme.scaffoldBackgroundColor),
          child: CommonTabBar.withAutoKey(
            tabData,
            currentIndex: _currentTabIndex,
            onTap: (index) {
              setState(() {
                _currentTabIndex = index;
              });
            },
            style: CommonTabBarStyle.line,
            isScrollable: false,
            backgroundColor: context.theme.scaffoldBackgroundColor,
            height: 44.gw,
          ),
        ),
        Expanded(
          child: IndexedStack(
            index: _currentTabIndex,
            children: [
              MultiBlocProvider(
                providers: [
                  BlocProvider(create: (_) => FTradeKLineCubit(FTradeKLineScrollRepository())),
                  BlocProvider<WatchListCubit>.value(value: context.read<WatchListCubit>()),
                ],
                child: FTradeKLineScrollView(
                  type: widget.type,
                  data: widget.data,
                  onChangeAllInfoScreenTitlesAction: widget.onChangeAllInfoScreenTitlesAction,
                ),
              ),
              BlocProvider(
                create: (_) => FTradeProfileScrollCubit()..fetchData(instrument: widget.data.makeInstrument()),
                child: BlocBuilder<FTradeProfileScrollCubit, FTradeProfileScrollState>(
                  builder: (context, state) {
                    return FTradeProfileScrollView(
                      type: widget.type,
                      data: widget.data,
                      displayList: state.makeDisplayList(),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
