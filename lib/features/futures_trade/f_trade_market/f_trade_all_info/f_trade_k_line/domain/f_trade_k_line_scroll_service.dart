import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_tick_model.dart';

class FTradeKLineScrollService {
  /// 期货信息
  static Future<FTradeInfoModel?> fetchFTradeInfo(CancelToken? cancelToken, {required String instrument}) async {
    final response = await Http().request<FTradeInfoModel>(
      ApiEndpoints.getFuturesMarketStockInfo,
      method: HttpMethod.get,
      queryParameters: {'instrument': instrument},
      cancelToken: cancelToken,
    );
    return response.data;
  }

  /// 期货K线数据
  ///
  /// period=> 1min/5min/15min/30min/60min/day/week/month/year
  static Future<FTradeKLineModel?> fetchFTradeKLine(
    CancelToken? cancelToken, {
    required String instrument,
    required String period,
  }) async {
    final response = await Http().request<FTradeKLineModel>(
      ApiEndpoints.getFuturesMarketKLine,
      method: HttpMethod.get,
      queryParameters: {'instrument': instrument, 'period': period},
      cancelToken: cancelToken,
    );
    return response.data;
  }

  /// 期货分时数据
  ///
  /// period=> day/5day
  static Future<FTradeKLineModel?> fetchFTradeTimeLine(CancelToken? cancelToken,
      {required String instrument, required String period}) async {
    final response = await Http().request<FTradeKLineModel>(
      ApiEndpoints.getFuturesMarketTimeLine,
      method: HttpMethod.get,
      queryParameters: {'instrument': instrument, 'period': period},
      cancelToken: cancelToken,
    );
    return response.data;
  }

  /// 逐笔成交数据
  static Future<FTradeTickModel?> fetchTickList({required String instrument, required int pageNumber}) async {
    final response = await Http().request<FTradeTickModel>(
      ApiEndpoints.getFuturesMarketTickList,
      method: HttpMethod.get,
      queryParameters: {'instrument': instrument, 'pageSize': 100, 'pageNumber': pageNumber},
    );
    return response.data;
  }

  /// 期货深度交易数据
  static Future<FTradeDepthModel?> fetchFTradeDepth(CancelToken? cancelToken, {required String instrument}) async {
    final response = await Http().request<FTradeDepthModel>(
      ApiEndpoints.getFuturesMarketDepthL2,
      method: HttpMethod.get,
      queryParameters: {'instrument': instrument, 'depth': 10},
      cancelToken: cancelToken,
    );
    return response.data;
  }
}
