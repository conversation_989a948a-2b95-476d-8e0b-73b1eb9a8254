part of 'f_trade_buy_sell_cubit.dart';

class FTradeBuySellState extends Equatable {
  /// 产品代码
  final String productCode;

  /// 用户可用余额
  final double? accountUsableCash;

  /// 期货证劵最新价格
  final double? fTradeLatestPrice;

  /// 期货证券最新一笔交易时间
  final int? fTradeLastTradeTime;

  /// 配置信息数据获取状态
  final DataStatus fTradeConfigDataStatus;

  /// 买入手续费
  final List<TradeHandlingFeeConfigEntity> buyCalculateConfigList;

  /// 卖出手续费
  final List<TradeHandlingFeeConfigEntity> sellCalculateConfigList;

  /// 配置数据
  final FTradeConfigModel? fTradeConfigModel;

  /// 市场状态(开关盘信息)
  final FTradeStateModel? fTradeStateModel;

  /// 用户当前持仓数据
  final List<FTradeAcctOrderRecords>? fTradeAcctPositionRecords;

  /// 用户输入控制器
  final UserInputController userInputController;

  /// 开多/平多控制器
  final FtradeLongShortActionsController longActionsController;

  /// 开空/平空控制器
  final FtradeLongShortActionsController shortActionsController;

  FTradeAcctOrderRecords? selectedHandlePosition() {
    if (longActionsController.selectedOptionIdx == 0) {
      return selectedLongPosition();
    }
    if (shortActionsController.selectedOptionIdx == 0) {
      return selectedShortPosition();
    }
    return null;
  }

  FTradeAcctOrderRecords? selectedLongPosition() {
    FTradeAcctOrderRecords? longPosition = fTradeAcctPositionRecords?.findFirstLongOrder();
    if (longActionsController.selectedOptionIdx == 0) {
      return longPosition;
    }
    return null;
  }

  FTradeAcctOrderRecords? selectedShortPosition() {
    FTradeAcctOrderRecords? shortPosition = fTradeAcctPositionRecords?.findFirstShortOrder();
    if (shortActionsController.selectedOptionIdx == 0) {
      return shortPosition;
    }
    return null;
  }

  FTradeAcctOrderRecords? holdLongPosition() {
    return fTradeAcctPositionRecords?.findFirstLongOrder();
  }

  FTradeAcctOrderRecords? holdShortPosition() {
    return fTradeAcctPositionRecords?.findFirstShortOrder();
  }

  const FTradeBuySellState({
    this.productCode = '',
    this.accountUsableCash,
    this.fTradeLatestPrice,
    this.fTradeLastTradeTime,
    this.fTradeConfigDataStatus = DataStatus.loading,
    this.fTradeConfigModel,
    this.fTradeStateModel,
    this.fTradeAcctPositionRecords,
    this.buyCalculateConfigList = const [],
    this.sellCalculateConfigList = const [],
    this.userInputController = const UserInputController(),
    this.longActionsController = const FtradeLongShortActionsController(),
    this.shortActionsController = const FtradeLongShortActionsController(),
  });

  FTradeBuySellState copyWith({
    String? productCode,
    double? accountUsableCash,
    double? fTradeLatestPrice,
    int? fTradeLastTradeTime,
    DataStatus? fTradeConfigDataStatus,
    FTradeConfigModel? fTradeConfigModel,
    FTradeStateModel? fTradeStateModel,
    List<FTradeAcctOrderRecords>? fTradeAcctPositionRecords,
    List<TradeHandlingFeeConfigEntity>? buyCalculateConfigList,
    List<TradeHandlingFeeConfigEntity>? sellCalculateConfigList,
    UserInputController? userInputController,
    FtradeLongShortActionsController? longActionsController,
    FtradeLongShortActionsController? shortActionsController,
  }) {
    return FTradeBuySellState(
      productCode: productCode ?? this.productCode,
      accountUsableCash: accountUsableCash ?? this.accountUsableCash,
      fTradeLatestPrice: fTradeLatestPrice ?? this.fTradeLatestPrice,
      fTradeLastTradeTime: fTradeLastTradeTime ?? this.fTradeLastTradeTime,
      fTradeConfigDataStatus: fTradeConfigDataStatus ?? this.fTradeConfigDataStatus,
      fTradeAcctPositionRecords: fTradeAcctPositionRecords ?? this.fTradeAcctPositionRecords,
      buyCalculateConfigList: buyCalculateConfigList ?? this.buyCalculateConfigList,
      sellCalculateConfigList: sellCalculateConfigList ?? this.sellCalculateConfigList,
      fTradeConfigModel: fTradeConfigModel ?? this.fTradeConfigModel,
      fTradeStateModel: fTradeStateModel ?? this.fTradeStateModel,
      userInputController: userInputController ?? this.userInputController,
      longActionsController: longActionsController ?? this.longActionsController,
      shortActionsController: shortActionsController ?? this.shortActionsController,
    );
  }

  @override
  List<Object?> get props => [
        accountUsableCash,
        fTradeLatestPrice,
        fTradeLastTradeTime,
        fTradeConfigDataStatus,
        fTradeAcctPositionRecords,
        buyCalculateConfigList,
        sellCalculateConfigList,
        fTradeConfigModel,
        fTradeStateModel,
        userInputController,
        longActionsController,
        shortActionsController,
      ];
}

/*
============================================================================================================================
FtradebuysellActionsController
============================================================================================================================
*/

/// 期货交易用户输入数量模式枚举
enum FTradeBuySellUserInputNumberEnum {
  /// 初始化
  init,

  /// 选择 1/4 1/3 1/2 全仓中进行选择
  selcted,

  /// 用键盘编辑
  input
}

class UserInputController extends Equatable {
  /// 开仓或平仓
  final TradeDirection tradeDirection;

  /// 数量编辑模式
  final FTradeBuySellUserInputNumberEnum inputNumberEnum;

  /// 价格
  final double price;

  /// 聚焦价格输入键盘
  final bool priceHasFocus;

  /// 数量
  final double number;

  /// 聚焦数量输入键盘
  final bool numberHasFocus;

  /// 分仓比例
  final OrderFraction? orderFraction;

  /// 市价单 限价单
  final PriceType priceType;

  /// 数量能否减
  final bool canNumberDecrement;

  /// 数量能否加
  final bool canNumberIncrement;

  /// 价格能否减
  final bool canPriceDecrement;

  const UserInputController({
    this.tradeDirection = TradeDirection.buy,
    this.inputNumberEnum = FTradeBuySellUserInputNumberEnum.init,
    this.price = -1,
    this.priceHasFocus = false,
    this.number = -1,
    this.numberHasFocus = false,
    this.orderFraction,
    this.priceType = PriceType.market,
    this.canNumberDecrement = false,
    this.canNumberIncrement = false,
    this.canPriceDecrement = false,
  });

  UserInputController copyWith({
    TradeDirection? tradeDirection,
    FTradeBuySellUserInputNumberEnum? inputNumberEnum,
    double? price,
    bool? priceHasFocus,
    double? number,
    bool? numberHasFocus,
    OrderFraction? orderFraction,
    PriceType? priceType,
    bool? canNumberDecrement,
    bool? canNumberIncrement,
    bool? canPriceDecrement,
  }) {
    return UserInputController(
      tradeDirection: tradeDirection ?? this.tradeDirection,
      inputNumberEnum: inputNumberEnum ?? this.inputNumberEnum,
      price: price ?? this.price,
      priceHasFocus: priceHasFocus ?? this.priceHasFocus,
      number: number ?? this.number,
      numberHasFocus: numberHasFocus ?? this.numberHasFocus,
      orderFraction: this.orderFraction == orderFraction ? this.orderFraction : orderFraction,
      priceType: priceType ?? this.priceType,
      canNumberDecrement: canNumberDecrement ?? this.canNumberDecrement,
      canNumberIncrement: canNumberIncrement ?? this.canNumberIncrement,
      canPriceDecrement: canPriceDecrement ?? this.canPriceDecrement,
    );
  }

  @override
  List<Object?> get props => [
        tradeDirection,
        inputNumberEnum,
        price,
        priceHasFocus,
        number,
        numberHasFocus,
        orderFraction,
        priceType,
        canNumberDecrement,
        canNumberIncrement,
        canPriceDecrement,
      ];
}

/*
============================================================================================================================
FtradebuysellActionsController
============================================================================================================================
*/

/// 交易买卖操作控制器，封装 UI 所需的参数和交互回调
class FtradeLongShortActionsController extends Equatable {
  /// 是否为买入操作（true=买入，false=卖出）
  final bool isBuy;

  int isBuyIntKey() {
    if (isBuy) {
      return 1;
    }
    return 2;
  }

  /// 显示在 UI 中的每一行信息（如价格、数量等）
  /// { 'title': 'DDD',
  ///   'amount': 789.34,
  ///   'currency': 'USDT',
  ///   'fractionDigits': 2,         =>小数点位数
  ///   'showTotalToolTip': true,}   =>是否显示问号(?)和弹窗
  final List<Map<String, dynamic>> displayRowInfoList;

  /// 确认按钮是否处于加载状态
  final bool isLoadingConfirmBtn;

  ///
  final String confirmTitle;

  ///
  final bool confirmEnabled;

  /// 点击确认按钮时的回调函数
  final VoidCallback? onConfirmPressed;

  /// 是否需要显示选项
  final bool needShowOptions;

  /// 选项标题列表
  final List<String> optionTitles;

  /// 当前选中的选项下标
  final int? selectedOptionIdx;

  /// 当选项被点击选中时的回调，传入的是下标索引
  final ValueChanged<int>? onOptionCellSelected;

  const FtradeLongShortActionsController({
    this.isBuy = true,
    this.displayRowInfoList = const [],
    this.isLoadingConfirmBtn = true,
    this.confirmTitle = '',
    this.confirmEnabled = true,
    this.onConfirmPressed,
    this.needShowOptions = false,
    this.optionTitles = const [],
    this.selectedOptionIdx,
    this.onOptionCellSelected,
  });

  static List<Map<String, dynamic>> makeDisplayRowInfoList(
    String buyOrSell,
    double numberRow0,
    double numberRow1,
    double numberRow2,
    double numberRow3,
    double? marginRatio,
  ) {
    return [
      {
        'title': buyOrSell == "buy" ? 'availableToBuy'.tr() : 'availableToSell'.tr(),
        'amount': numberRow0,
        'suffix': 'lotForStockIndex'.tr(), // 'lotForSecurities'.tr()
        'fractionDigits': 0,
      },
      {
        'title': 'margin_ratio'.tr(),
        'amount': marginRatio,
        'suffix': '%',
        'fractionDigits': 2,
      },
      {'title': 'transactionFee'.tr(), 'amount': numberRow1},
      {'title': 'required_margin'.tr(), 'amount': numberRow2},
      {'title': 'totalPrice'.tr(), 'amount': numberRow3, 'showTotalToolTip': true},
    ];
  }

  /// 默认值初始化函数
  static FtradeLongShortActionsController defaultInit({required bool isBuy}) {
    return FtradeLongShortActionsController(
      isBuy: isBuy,
      displayRowInfoList: makeDisplayRowInfoList('buy', 0.0, 0.0, 0.0, 0.0, 0.0),
      isLoadingConfirmBtn: true,
      confirmTitle: '',
      onConfirmPressed: () {},
      needShowOptions: false,
      optionTitles: [],
      selectedOptionIdx: null,
      onOptionCellSelected: null,
    );
  }

  FtradeLongShortActionsController copyWith({
    bool? isBuy,
    List<Map<String, dynamic>>? displayRowInfoList,
    bool? isLoadingConfirmBtn,
    String? confirmTitle,
    bool? confirmEnabled,
    VoidCallback? onConfirmPressed,
    bool? needShowOptions,
    List<String>? optionTitles,
    int? selectedOptionIdx,
    ValueChanged<int>? onOptionCellSelected,
  }) {
    return FtradeLongShortActionsController(
      isBuy: isBuy ?? this.isBuy,
      displayRowInfoList: displayRowInfoList ?? this.displayRowInfoList,
      isLoadingConfirmBtn: isLoadingConfirmBtn ?? this.isLoadingConfirmBtn,
      confirmTitle: confirmTitle ?? this.confirmTitle,
      confirmEnabled: confirmEnabled ?? this.confirmEnabled,
      onConfirmPressed: onConfirmPressed ?? this.onConfirmPressed,
      needShowOptions: needShowOptions ?? this.needShowOptions,
      optionTitles: optionTitles ?? this.optionTitles,
      selectedOptionIdx: selectedOptionIdx ?? this.selectedOptionIdx,
      onOptionCellSelected: onOptionCellSelected ?? this.onOptionCellSelected,
    );
  }

  @override
  List<Object?> get props => [
        isBuy,
        displayRowInfoList,
        isLoadingConfirmBtn,
        needShowOptions,
        confirmEnabled,
        confirmTitle,
        optionTitles,
        selectedOptionIdx,
      ];
}
