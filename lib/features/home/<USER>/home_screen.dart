import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/home_app_bar.dart';
import 'package:gp_stock_app/features/home/<USER>/home_banner.dart';
import 'package:gp_stock_app/features/home/<USER>/home_market_tab_section.dart';
import 'package:gp_stock_app/features/home/<USER>/home_marquee_text.dart';
import 'package:gp_stock_app/features/home/<USER>/home_menu.dart';
import 'package:gp_stock_app/features/home/<USER>/home_news.dart';
import 'package:gp_stock_app/features/home/<USER>/settings_menu.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market/logic/search/search_cubit.dart';
import 'package:gp_stock_app/features/market/market_search_screen.dart';
import 'package:gp_stock_app/shared/mixins/locale_aware_mixins.dart';

import '../../account/logic/account/account_cubit.dart';
import '../../activity/logic/activity/activity_cubit.dart';
import '../../market/logic/market/market_cubit.dart';
import '../../notifications/logic/notifications/notifications_cubit.dart';
import '../logic/home/<USER>';
import '../logic/news/news_cubit.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver, LocaleAwareScreenMixin {
  int _appLifecycleStatePausedTimeSecond = 0;

  @override
  void initState() {
    super.initState();
    getIt<IndexTradeCubit>().subscribeToTimeline();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void onLocaleChanged(_) => _initialFunction();

  Future<void> _initialFunction() async {
    final currentContext = context;
    if (!currentContext.mounted) return;
    context.read<HomeCubit>().getBannerList();
    context.read<AccountCubit>().getContractSummary();
    context.read<MarketCubit>().fetchTableData(isHome: true);
    context.read<NotificationsCubit>().getNotificationCount();
    context.read<ActivityCubit>().getTasks();
    context.read<NewsCubit>().getNews();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    int now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    if (state == AppLifecycleState.resumed && (now - _appLifecycleStatePausedTimeSecond) > 30) {
      // 超过 30 秒没用，回来时刷新交易时间线
      getIt<IndexTradeCubit>().reloadTimeline();
    } else if (state == AppLifecycleState.paused) {
      // 记录当前时间戳（秒）
      // 用来和下次回到前台时比较
      _appLifecycleStatePausedTimeSecond = now;
    }
  }

  @override
  void dispose() {
    getIt<IndexTradeCubit>().unsubscribeFromTimeline();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _buildMainLayout(
      context,
      children: _buildChildren(context),
      onRefresh: () async => await _initialFunction(),
    );
  }
}

Widget _buildMainLayout(
  BuildContext context, {
  required List<Widget> children,
  required Future<void> Function() onRefresh,
}) {
  final commonScaffold = Scaffold(
    backgroundColor: Colors.transparent,
    body: Column(
      spacing: 10.gw,
      children: [
        /// yhxt需要专用顶部导航栏，其他的在mainScreen中处理了
        if (AppConfig.instance.flavor == Flavor.yhxt) ...[
          HomeAppBar(
            title: "appName".tr(),
            subtitle: "上海沅和股权投资基金管理有限公司",
            logo: 'assets/images/logo/app_logo.png',
            menuIcon: 'assets/images/icon_setting.png',
            searchIcon: 'assets/images/icon_search.png',
            onMenuIconTap: () => onMenuIconTap(context),
            onSearchIconTap: () => onSearchIconTap(context),
          ),
        ],
        Expanded(
          child: RefreshIndicator.adaptive(
            backgroundColor: context.theme.cardColor,
            onRefresh: onRefresh,
            child: AnimationLimiter(
              child: SingleChildScrollView(
                child: AnimationLimiter(
                  child: Column(
                    spacing: 10.gw,
                    children: AnimationConfiguration.toStaggeredList(
                      children: children,
                      duration: const Duration(milliseconds: 300),
                      childAnimationBuilder: (widget) => SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: widget,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    ),
  );

  return switch (AppConfig.instance.flavor) {
    Flavor.yhxt => Stack(
        children: [
          // 背景图
          Image.asset(
            "assets/images/bg_home.png",
            fit: BoxFit.fitWidth,
            alignment: Alignment.topCenter,
          ),

          // 渐变覆盖层
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color.fromRGBO(255, 255, 255, 0.0), // 透明
                  Color(0xFFF4F7FE), // 渐变到 #F4F7FE
                ],
                stops: [0.0002, 0.638], // 对应 0.02% 和 63.8%
              ),
            ),
          ),
          commonScaffold,
        ],
      ),
    Flavor.xyzq || Flavor.dyzb => Stack(
        children: [
          ClipPath(
            clipper: _BottomArcClipper(),
            child: Container(
              height: 100, // 50(矩形) + 50(椭圆)
              decoration: BoxDecoration(
                color: context.theme.appBarTheme.backgroundColor,
              ),
            ),
          ),
          commonScaffold,
        ],
      ),
    _ => commonScaffold,
  };
}

List<Widget> _buildChildren(BuildContext context) {
  return [
    SizedBox(height: 5.gw),
    ...sortList.map((e) => switch (e) {
          HomeSortType.banner => const HomeBanner(),
          HomeSortType.marquee => const HomeMarqueeText(),
          HomeSortType.menu => const HomeMenu(),
          HomeSortType.marketTabs => const HomeMarketTabSection(),
          HomeSortType.newsAndEvents => const HomeNewsSection(),
        }),
  ];
}

void onMenuIconTap(BuildContext context) {
  final RenderBox button = context.findRenderObject() as RenderBox;
  final buttonPosition = button.localToGlobal(Offset.zero);
  final RelativeRect position = RelativeRect.fromSize(
    Rect.fromLTRB(buttonPosition.dx + button.size.width - 200.gw, buttonPosition.dy + 50.gw,
        buttonPosition.dx + button.size.width, buttonPosition.dy + 100.gw),
    Size(110, 160),
  );
  showMenu(
    context: context,
    position: position,
    elevation: 0,
    color: Colors.transparent,
    constraints: BoxConstraints(maxWidth: 200.gw),
    items: [
      PopupMenuItem(
        enabled: false,
        padding: EdgeInsets.zero,
        child: SettingsMenu(),
      ),
    ],
  );
}

void onSearchIconTap(BuildContext context) {
  AuthUtils.verifyAuth(
    () => Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => getIt<SearchCubit>(),
          child: const MarketSearchScreen(),
        ),
      ),
    ),
  );
}

List<HomeSortType> get sortList {
  if (AppConfig.instance.flavor == Flavor.yhxt) {
    return [
      HomeSortType.marquee,
      HomeSortType.banner,
      HomeSortType.menu,
      HomeSortType.marketTabs,
      HomeSortType.newsAndEvents
    ];
  }

  return switch (AppConfig.instance.skinStyle) {
    AppSkinStyle.kGP || AppSkinStyle.kTemplateD => [
        HomeSortType.menu,
        HomeSortType.banner,
        HomeSortType.marquee,
        HomeSortType.marketTabs,
        HomeSortType.newsAndEvents
      ],
    _ => [
        HomeSortType.banner,
        HomeSortType.menu,
        HomeSortType.marquee,
        HomeSortType.marketTabs,
        HomeSortType.newsAndEvents
      ],
  };
}

class _BottomArcClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();

    // 绘制100高度的矩形部分
    path.moveTo(0, 0); // 左上角
    path.lineTo(size.width, 0); // 右上角
    path.lineTo(size.width, 50); // 右边到矩形底部

    // 绘制50高度的半椭圆部分
    path.quadraticBezierTo(
      size.width / 2, 100, // 控制点：100(50+50)
      0, 50, // 终点：左边矩形底部
    );

    // 闭合路径
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
