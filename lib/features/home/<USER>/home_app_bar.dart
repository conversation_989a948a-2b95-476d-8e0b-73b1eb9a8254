import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/notifications/logic/notifications/notifications_cubit.dart';

class HomeAppBar extends StatelessWidget {
  const HomeAppBar({
    super.key,
    required this.logo,
    required this.title,
    this.subtitle,
    required this.menuIcon,
    required this.searchIcon,
    required this.onMenuIconTap,
    required this.onSearchIconTap,
  });

  final String logo;
  final String title;
  final String? subtitle;
  final String menuIcon;
  final String searchIcon;
  final VoidCallback onMenuIconTap;
  final VoidCallback onSearchIconTap;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      surfaceTintColor: Colors.transparent,
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: false,
      leadingWidth: 56.gw,
      actionsPadding: EdgeInsets.only(right: 16.gw),
      title: _buildTitle(title: title, subTitle: subtitle),
      leading: _buildLeading(),
      actions: _buildActions(onMenuIconTap, onSearchIconTap),
    );
  }
}

Widget? _buildTitle({title, subTitle}) {
  return switch (AppConfig.instance.flavor) {
    Flavor.yhxt => Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(color: Colors.white, fontSize: 18.gsp, fontWeight: FontWeight.w500),
          ),
          if (subTitle != null) ...[
            SizedBox(height: 2.gw),
            Text(
              subTitle,
              style: TextStyle(color: Colors.white, fontSize: 10.gsp),
            ),
          ],
        ],
      ),
    _ => null,
  };
}

Widget? _buildLeading() {
  return Padding(
    padding: EdgeInsets.only(left: 16.gw),
    child: switch (AppConfig.instance.flavor) {
      Flavor.yhxt => ClipRRect(
          borderRadius: BorderRadius.circular(7), // 圆角半径
          child: Image.asset(
            "assets/images/logo/app_logo.png",
          ),
        ),
      _ => null,
    },
  );
}

List<Widget>? _buildActions(VoidCallback onMenuIconTap, VoidCallback onSearchIconTap) {
  return switch (AppConfig.instance.flavor) {
    Flavor.yhxt => [
        InkWell(
          onTap: () => onMenuIconTap(),
          child: BlocSelector<NotificationsCubit, NotificationsState, int?>(
            selector: (state) => state.notificationCount,
            builder: (context, notificationCount) {
              return Badge(
                offset: const Offset(10, -10),
                isLabelVisible: notificationCount != null && notificationCount > 0,
                child: Image.asset(
                  "assets/images/icon_setting.png",
                  width: 26.gw,
                  height: 26.gw,
                ),
              );
            },
          ),
        ),
        SizedBox(width: 5.gw),
        InkWell(
          onTap: onSearchIconTap,
          child: Container(
            width: 40.gw,
            height: 40.gw,
            alignment: Alignment.center,
            child: Image.asset(
              "assets/images/icon_search.png",
              width: 26.gw,
              height: 26.gw,
            ),
          ),
        ),
      ],
    _ => null,
  };
}
