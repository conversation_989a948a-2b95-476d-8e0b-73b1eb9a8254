import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/home/<USER>/home/<USER>';
import 'package:gp_stock_app/features/home/<USER>/news/news_cubit.dart';
import 'package:gp_stock_app/features/home/<USER>/news/news_state.dart';
import 'package:gp_stock_app/features/home/<USER>/news/news_content.dart';
import 'package:gp_stock_app/features/home/<USER>/news/news_image.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class HomeNewsSection extends StatelessWidget {
  const HomeNewsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return parentContainer(
      header: _buildHeader(context),
      content: CommonNewsList(),
    );
  }

  Widget parentContainer({
    required Widget header,
    required Widget content,
  }) =>
      switch (AppConfig.instance.flavor) {
        Flavor.yhxt => Container(
            margin: EdgeInsets.symmetric(horizontal: 15.gw),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.gw),
            ),
            child: Stack(
              children: [
                Positioned.fill(
                  child: Container(
                    color: Colors.white,
                  ),
                ),
                Container(
                  width: 195.gw,
                  height: 195.gw,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topLeft, // 左上角
                        end: Alignment.bottomRight, // 右下角
                        colors: [
                          Color(0xFFd3e8fd), // #d2e7fc
                          Colors.white, // #ffffff
                        ],
                        stops: [
                          0,
                          0.25,
                        ]),
                  ),
                ),
                Container(
                  padding: EdgeInsets.fromLTRB(16.gw, 11.gw, 12.gw, 26.gw),
                  child: Column(
                    children: [
                      header,
                      SizedBox(height: 10.gw),
                      content,
                    ],
                  ),
                ),
              ],
            ),
          ),
        _ => ShadowBox(
            borderRadius: getBorderRadius,
            margin: getMargin,
            child: Column(
              children: [
                Row(
                  children: [
                    header,
                  ],
                ),
                10.verticalSpace,
                content,
                10.verticalSpace,
              ],
            ),
          ),
      };
  EdgeInsets? get getMargin => switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateC => EdgeInsets.symmetric(horizontal: 12.gw),
        _ => null,
      };

  Widget _buildHeader(BuildContext context) {
    return switch (AppConfig.instance.flavor) {
      Flavor.yhxt => Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              "assets/images/icon_home_news.png",
              width: 24.gw,
              height: 28.gw,
            ),
            SizedBox(width: 15.gw),
            Text(
              "todayNews".tr(), // 今日资讯
              style: TextStyle(color: context.colorTheme.tabActive).fs16.w500,
            ),
          ],
        ),
      _ => Row(
          children: [
            MarketTableHeaderStyle(
              title: tr("todayNews", context: context),
              isSelected: true,
              onTap: () => context.read<HomeCubit>().updateNewsTab(0),
              padding: switch (AppConfig.instance.flavor) {
                Flavor.gp || Flavor.rsyp || Flavor.pre => EdgeInsets.symmetric(horizontal: 16.gw, vertical: 5.gw),
                _ => null,
              },
              radius: switch (AppConfig.instance.flavor) {
                Flavor.gp || Flavor.rsyp || Flavor.pre => 8.gr,
                _ => null,
              },
            ),
          ],
        ),
    };
  }
}

class CommonNewsList extends StatelessWidget {
  const CommonNewsList({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NewsCubit, NewsState>(
      builder: (context, state) {
        if (state.status == DataStatus.loading) {
          return const NewsListShimmer();
        }

        if (state.status == DataStatus.failed) {
          return Center(child: TableEmptyWidget(height: 50.gw, width: 50.gw));
        }

        final news = state.newsData?.records ?? [];
        if (news.isEmpty) {
          return const Center(child: Text('No news available'));
        }

        return AnimationLimiter(
          child: ListView.separated(
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: news.length,
            shrinkWrap: true,
            separatorBuilder: (_, __) => Divider(
              height: 24.gw,
              thickness: 1,
              color: context.theme.primaryColor.withValues(alpha: 0.05),
            ),
            itemBuilder: (context, index) {
              final item = news[index];
              return AnimationConfiguration.staggeredList(
                position: index,
                duration: const Duration(milliseconds: 600),
                child: SlideAnimation(
                  verticalOffset: 30.0,
                  child: FadeInAnimation(
                    child: Bounceable(
                      onTap: () => getIt<NavigatorService>().push(
                        AppRouter.routeNewsDetails,
                        arguments: item,
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: switch (AppConfig.instance.flavor) {
                          Flavor.yhxt => [
                              Expanded(
                                child: NewsContent(
                                    news: item,
                                    titleStyle: TextStyle(color: Colors.black, fontSize: 14.gsp),
                                    authorStyle: TextStyle(color: Color(0xff0052FF), fontSize: 12.gsp),
                                    timeStyle: TextStyle(color: Color(0xff525A79), fontSize: 12.gsp)),
                              ),
                              12.horizontalSpace,
                              Hero(
                                tag: item.id,
                                child: NewsImage(imageUrl: item.url),
                              ),
                            ],
                          _ => switch (AppConfig.instance.skinStyle) {
                              AppSkinStyle.kTemplateC => [
                                  Expanded(
                                    child: NewsContent(
                                      news: item,
                                    ),
                                  ),
                                  12.horizontalSpace,
                                  Hero(
                                    tag: item.id,
                                    child: NewsImage(imageUrl: item.url),
                                  ),
                                ],
                              _ => [
                                  Hero(
                                    tag: item.id,
                                    child: NewsImage(imageUrl: item.url),
                                  ),
                                  12.horizontalSpace,
                                  Expanded(
                                    child: NewsContent(
                                      news: item,
                                    ),
                                  ),
                                ],
                            },
                        },
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

class NewsListShimmer extends StatelessWidget {
  final int itemCount;

  const NewsListShimmer({
    super.key,
    this.itemCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: itemCount,
      separatorBuilder: (_, __) => Divider(
        height: 24.gw,
        thickness: 1,
        color: context.theme.primaryColor.withValues(alpha: 0.05),
      ),
      itemBuilder: (context, index) => const NewsListItemShimmer(),
    );
  }
}

class NewsListItemShimmer extends StatelessWidget {
  const NewsListItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final imageWidget = Container(
      width: 120.gw,
      height: 80.gw,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
      ),
    );
    final textWidget = Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Shimmer for Title
          Container(
            width: double.infinity,
            height: 16.gw,
            color: context.theme.cardColor,
            margin: EdgeInsets.only(bottom: 8.gw),
          ),
          // Shimmer for Second Title Line
          Container(
            width: 0.7.gsw,
            height: 16.gw,
            color: context.theme.cardColor,
            margin: EdgeInsets.only(bottom: 8.gw),
          ),
          // Shimmer for Date
          Container(
            width: 100.gw,
            height: 12.gw,
            color: context.theme.cardColor,
          ),
        ],
      ),
    );
    return ShimmerWidget(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.gw),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: switch (AppConfig.instance.flavor) {
            Flavor.yhxt => [
                textWidget,
                16.horizontalSpace,
                imageWidget,
              ],
            _ => switch (AppConfig.instance.skinStyle) {
                AppSkinStyle.kTemplateC => [
                    textWidget,
                    16.horizontalSpace,
                    imageWidget,
                  ],
                _ => [
                    // Shimmer for Image
                    imageWidget,
                    16.horizontalSpace,
                    textWidget,
                  ],
              },
          },
        ),
      ),
    );
  }
}

BorderRadius? get getBorderRadius => switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateD => BorderRadius.circular(0),
      _ => null,
    };

class MarketTableHeaderStyle extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;
  final bool disableScaleAnimation;
  final EdgeInsets? padding;
  final Color? activeColor;
  final TextStyle? activeTextStyle;
  final Color? inactiveColor;
  final TextStyle? inactiveTextStyle;
  final double? radius;

  const MarketTableHeaderStyle({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
    this.disableScaleAnimation = false,
    this.padding,
    this.activeColor,
    this.activeTextStyle,
    this.inactiveColor,
    this.inactiveTextStyle,
    this.radius,
  });

  @override
  Widget build(BuildContext context) {
    TextStyle textStyle = context.textTheme.regular.copyWith(
      color: isSelected ? Colors.white : context.colorTheme.textRegular,
    );
    if (activeTextStyle != null && isSelected) {
      textStyle = activeTextStyle!;
    }
    if (inactiveTextStyle != null && !isSelected) {
      textStyle = inactiveTextStyle!;
    }
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? (activeColor ?? context.theme.primaryColor) : (inactiveColor ?? Colors.transparent),
          borderRadius: BorderRadius.circular(radius ?? 4),
        ),
        child: Text(
          title,
          style: textStyle,
        ),
      ),
    );
  }
}
