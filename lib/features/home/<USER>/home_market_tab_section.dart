import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/home/<USER>/market_data_table.dart';
import 'package:gp_stock_app/features/market/watch_list/widgets/wishlist_data_table.dart';
import 'package:gp_stock_app/features/market/widgets/visual_graph_list.dart';
import 'package:gp_stock_app/features/market/widgets/visual_graph_section.dart';
import 'package:gp_stock_app/features/market/widgets/yhxt_visual_graph_section.dart';
import 'package:gp_stock_app/shared/constants/enums/market_tab_type.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';

class HomeMarketTabSection extends StatefulWidget {
  const HomeMarketTabSection({super.key});

  @override
  State<HomeMarketTabSection> createState() => _HomeMarketTabSectionState();
}

class _HomeMarketTabSectionState extends State<HomeMarketTabSection> {
  int _selectedTabIndex = 0;
  late List<MarketTabType> tabTypes;

  @override
  void initState() {
    super.initState();
    TradingMode currentMode = AppConfig.instance.tradingMode;
    final sysSettingsState = context.read<SysSettingsCubit>().state;

    sysSettingsState.maybeWhen(
      loaded: (_, config) {
        currentMode = TradingModeExtension.fromIndex(config.tradingMode);
      },
      orElse: () {},
    );

    switch (currentMode) {
      case TradingMode.stock:
        tabTypes = [MarketTabType.stockIndex, MarketTabType.stocks, MarketTabType.watchList];
        break;
      case TradingMode.futures:
        tabTypes = [MarketTabType.titleFutures, MarketTabType.watchList];
        break;
      case TradingMode.stockAndFutures:
        tabTypes = [
          MarketTabType.stockIndex,
          MarketTabType.stocks,
          MarketTabType.titleFutures,
          MarketTabType.watchList
        ];
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return HomeMarketTab(
      selectedTabIndex: _selectedTabIndex,
      tabTypes: tabTypes,
      onTabChanged: _onTabChanged,
      header: _buildHeader(context),
      tabHeader: _buildTabHeader(
        tabTypes: tabTypes,
        selectedTabIndex: _selectedTabIndex,
        onTap: _onTabChanged,
      ),
      tabContent: _buildTabContent(tabTypes: tabTypes, selectedTabIndex: _selectedTabIndex),
    );
  }

  Widget _buildTabContent({required List<MarketTabType> tabTypes, required int selectedTabIndex}) {
    final tabType = tabTypes[selectedTabIndex];
    return switch (tabType) {
      MarketTabType.stockIndex => switch (AppConfig.instance.flavor) {
          Flavor.yhxt => YhxtVisualGraphSection(isFromHome: true),
          _ => switch (AppConfig.instance.skinStyle) {
              AppSkinStyle.kGP => VisualGraphSection(isFromHome: true),
              _ => VisualGraphList(),
            }
        },
      MarketTabType.stocks => switch (AppConfig.instance.flavor) {
          Flavor.yhxt => Padding(
              padding: EdgeInsets.only(right: 16.gw),
              child: const MarketDataTable(limit: 5, isHome: true),
            ),
          _ => switch (AppConfig.instance.skinStyle) {
              AppSkinStyle.kGP => Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.gw),
                  child: const MarketDataTable(limit: 5, isHome: true),
                ),
              _ => const MarketDataTable(limit: 5, isHome: true, showInCard: true),
            },
        },
      MarketTabType.titleFutures => MultiBlocProvider(
          providers: [
            BlocProvider<FTradeListCubit>(
              create: (_) => FTradeListCubit(FTradeListRepository(), showInHomePage: true),
            ),
          ],
          child: Padding(
            padding: switch (AppConfig.instance.flavor) {
              Flavor.yhxt => EdgeInsets.only(right: 16.gw),
              _ => switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kGP => EdgeInsets.symmetric(horizontal: 12.gw),
                  _ => EdgeInsets.zero,
                },
            },
            child: FTradeListScreen(
              showInHomePage: true,
              showInCard: switch (AppConfig.instance.flavor) {
                Flavor.yhxt => true,
                _ => switch (AppConfig.instance.skinStyle) {
                    AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateC => true,
                    _ => false,
                  },
              },
            ),
          ),
        ),
      MarketTabType.watchList => Padding(
          padding: switch (AppConfig.instance.flavor) {
            Flavor.yhxt => EdgeInsets.only(right: 16.gw),
            _ => switch (AppConfig.instance.skinStyle) {
                AppSkinStyle.kGP => EdgeInsets.symmetric(horizontal: 12.gw),
                _ => EdgeInsets.zero,
              },
          },
          child: WishListDataTable(
              limit: 5,
              isFromHome: true,
              showInCard: switch (AppConfig.instance.flavor) {
                Flavor.yhxt => null,
                _ => switch (AppConfig.instance.skinStyle) {
                    AppSkinStyle.kGP => null,
                    _ => true,
                  },
              }),
        ),
    };
  }

  Widget _buildTabHeader({
    required List<MarketTabType> tabTypes,
    required int selectedTabIndex,
    required Function(int) onTap,
  }) {
    final tabData = tabTypes.map((tabType) => tr(tabType.key, context: context)).toList();

    final tabStyle = switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateB => CommonTabBarStyle.line,
      AppSkinStyle.kTemplateC || AppSkinStyle.kTemplateD || AppSkinStyle.kGP => CommonTabBarStyle.rectangular,
    };

    return CommonTabBar.withAutoKey(
      tabData,
      currentIndex: selectedTabIndex,
      onTap: onTap,
      style: tabStyle,
      isScrollable: switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kGP => false,
        _ => true,
      },
      height: 30.gw,
    );
  }

  void _onTabChanged(int index) {
    setState(() {
      _selectedTabIndex = index;
    });
  }

  Widget _buildHeader(BuildContext context) {
    return switch (AppConfig.instance.flavor) {
      Flavor.yhxt => Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Image.asset(
                  "assets/images/icon_home_stock.png",
                  width: 24.gw,
                  height: 27.gw,
                ),
                SizedBox(width: 15.gw),
                Text(
                  "featuredStocks".tr(), // 精选股票
                  style: TextStyle(color: context.colorTheme.tabActive).fs16.w500,
                ),
              ],
            ),
            SizedBox(height: 12.gw),
          ],
        ),
      _ => SizedBox(),
    };
  }
}

class HomeMarketTab extends StatelessWidget {
  const HomeMarketTab({
    super.key,
    required this.selectedTabIndex,
    required this.tabTypes,
    required this.onTabChanged,
    required this.header,
    required this.tabHeader,
    required this.tabContent,
  });

  final int selectedTabIndex;
  final List<MarketTabType> tabTypes;
  final Function(int) onTabChanged;
  final Widget header;
  final Widget tabHeader;
  final Widget tabContent;
  @override
  Widget build(BuildContext context) {
    return _buildLayout(
      context,
      tabTypes: tabTypes,
      selectedTabIndex: selectedTabIndex,
      onTabChanged: onTabChanged,
      header: header,
      tabHeader: tabHeader,
      tabContent: tabContent,
    );
  }

  Clip get clipBehavior => switch (AppConfig.instance.flavor) { Flavor.yhxt => Clip.hardEdge, _ => Clip.none };

  EdgeInsets? get margin => switch (AppConfig.instance.flavor) {
        Flavor.yhxt => EdgeInsets.symmetric(horizontal: 15.gw),
        _ => switch (AppConfig.instance.skinStyle) {
            AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateC => EdgeInsets.symmetric(horizontal: 12.gw),
            _ => null,
          },
      };
  BorderRadius? get borderRadius => switch (AppConfig.instance.flavor) {
        Flavor.yhxt => BorderRadius.circular(12.gw),
        _ => switch (AppConfig.instance.skinStyle) {
            AppSkinStyle.kTemplateC => BorderRadius.circular(8.gw),
            AppSkinStyle.kTemplateA => BorderRadius.circular(4.gw),
            _ => null,
          },
      };
  BoxDecoration? decoration(BuildContext context) => switch (AppConfig.instance.flavor) {
        Flavor.yhxt => BoxDecoration(borderRadius: borderRadius),
        _ => switch (AppConfig.instance.skinStyle) {
            AppSkinStyle.kTemplateD => BoxDecoration(color: context.theme.cardColor),
            AppSkinStyle.kTemplateC => BoxDecoration(color: context.theme.cardColor, borderRadius: borderRadius),
            AppSkinStyle.kGP => BoxDecoration(color: Colors.transparent, borderRadius: borderRadius),
            AppSkinStyle.kTemplateA => BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: borderRadius,
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x0F354677),
                    offset: Offset(0, 4),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
              ),
            _ => null,
          },
      };

  EdgeInsets? get padding => switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kTemplateA ||
        AppSkinStyle.kTemplateC ||
        AppSkinStyle.kTemplateD =>
          EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gw),
        _ => null,
      };

  Widget _buildLayout(
    BuildContext context, {
    required List<MarketTabType> tabTypes,
    required int selectedTabIndex,
    required Function(int) onTabChanged,
    required Widget header,
    required Widget tabHeader,
    required Widget tabContent,
  }) {
    Widget parentContainer({
      required Widget child,
    }) {
      return Container(
        clipBehavior: clipBehavior,
        margin: margin,
        padding: padding,
        decoration: decoration(context),
        child: child,
      );
    }

    return parentContainer(
        child: switch (AppConfig.instance.flavor) {
      Flavor.yhxt => Stack(
          children: [
            Positioned.fill(
              child: Container(
                color: Colors.white,
              ),
            ),
            Container(
              width: 195.gw,
              height: 195.gw,
              padding: EdgeInsets.fromLTRB(16.gw, 11.gw, 0, 26.gw),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topLeft, // 左上角
                    end: Alignment.bottomRight, // 右下角
                    colors: [
                      Color(0xFFf6d0d1), // #d2e7fc
                      Colors.white, // #ffffff
                    ],
                    stops: [
                      0,
                      0.25,
                    ]),
              ),
            ),
            Container(
              padding: EdgeInsets.fromLTRB(16.gw, 11.gw, 0, 26.gw),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  header,
                  tabHeader,
                  SizedBox(height: 14.gw),
                  tabContent,
                ],
              ),
            ),
          ],
        ),
      _ => switch (AppConfig.instance.skinStyle) {
          AppSkinStyle.kGP => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 8.gw,
              children: [
                header,
                Container(
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.gw)),
                  margin: EdgeInsets.symmetric(horizontal: 16.gw),
                  child: tabHeader,
                ),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 2.gw),
                  child: tabContent,
                ),
              ],
            ),
          _ => Column(
              spacing: 16.gw,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                tabHeader,
                tabContent,
              ],
            ),
        }
    });
  }
}
