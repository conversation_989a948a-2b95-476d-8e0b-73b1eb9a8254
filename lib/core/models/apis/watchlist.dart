import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

/// 自选接口
class WatchlistApi {
  /// 通过Instrument获取自选Entity
  static Future<WatchlistItemEntity?> getWatchlistByInstrument(String instrument) async {
    final res = await Http().request<WatchlistItemEntity>(
      ApiEndpoints.getWatchListBySymbol,
      method: HttpMethod.get,
      queryParameters: {
        "market": instrument.split('|')[0],
        "securityType": instrument.split('|')[1],
        "symbol": instrument.split('|')[2],
      },
    );

    /// data为空的情况下，id等于默认值0，既没有自选
    return res.data?.id != 0 ? res.data : null;
  }

  /// 添加自选
  static Future<bool> addToWatchList({
    required String symbol,
    required String market,
    required int putSort,
    required String securityType,
    required int sort,
  }) async {
    final res = await Http().request(
      ApiEndpoints.addToWatchList,
      params: {
        "symbol": symbol,
        "market": market,
        "putSort": putSort,
        "securityType": securityType,
        "sort": sort,
      },
    );
    return res.isSuccess;
  }

  static Future<bool> removeFromWatchListBySymbol({
    required String symbol,
    required String securityType,
    required String market,
  }) async {
    final res = await Http().request(
      ApiEndpoints.removeFromWatchListBySymbol,
      method: HttpMethod.delete,
      queryParameters: {
        "symbol": symbol,
        "securityType": securityType,
        "market": market,
      },
    );
    return res.isSuccess;
  }

  /// 取消自选
  static Future<bool> removeFromWatchList(int choiceId) async {
    final res = await Http().request(
      ApiEndpoints.removeFromWatchList,
      method: HttpMethod.delete,
      queryParameters: {"id": choiceId},
    );

    return res.isSuccess;
  }

  /// 获取自选列表
  static Future<WatchlistListEntity?> getWatchList({
    int? pageNumber,
    int? pageSize,
    String? market,
    String? field,
    String? order,
  }) async {
    final res = await Http().request<WatchlistListEntity>(
      ApiEndpoints.getWatchList,
      method: HttpMethod.get,
      queryParameters: {
        'pageNumber': pageNumber,
        'pageSize': pageSize,
        'market': market,
      }..addAll({
          if (field != null) 'field': field,
          if (order != null) 'order': order,
        }),
    );
    return res.data;
  }

  /// 获取自选详情
  static Future<WatchlistItemEntity?> getWatchListDetail(String choiceId) async {
    final res = await Http().request<WatchlistItemEntity>(
      ApiEndpoints.getWatchListDetail,
      params: {"id": choiceId},
    );
    return res.data;
  }
}
