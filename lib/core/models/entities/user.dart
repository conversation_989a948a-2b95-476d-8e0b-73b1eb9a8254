import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/user.g.dart';
import 'dart:convert';

@JsonSerializable()
class UserModel {
	bool auth = false;
	int authStatus = 0;
	String avatar = '1';
	String countryCode = '';
	String email = '';
	int fromType = 0;
	int id = 0;
	String idCard = '';
	String imAccount = '';
	String inviteCode = '';
	bool isPayment = false;
	int level = 0;
	String mobile = '';
	String nickname = '';
	int pid = 0;
	String profiles = '';
	String realName = '';
	int score = 0;
	int sex = 0;
	bool status = false;
	int tradeStatus = 0;
	int type = 0;
	String uid = '';

	UserModel();

	factory UserModel.fromJson(Map<String, dynamic> json) => $UserModelFromJson(json);

	Map<String, dynamic> toJson() => $UserModelToJson(this);

	UserModel copyWith({
		bool? auth,
		int? authStatus,
		String? avatar,
		String? countryCode,
		String? email,
		int? fromType,
		int? id,
		String? idCard,
		String? imAccount,
		String? inviteCode,
		bool? isPayment,
		int? level,
		String? mobile,
		String? nickname,
		int? pid,
		String? profiles,
		String? realName,
		int? score,
		int? sex,
		bool? status,
		int? tradeStatus,
		int? type,
		String? uid,
	}) {
		return UserModel()
			..auth = auth ?? this.auth
			..authStatus = authStatus ?? this.authStatus
			..avatar = avatar ?? this.avatar
			..countryCode = countryCode ?? this.countryCode
			..email = email ?? this.email
			..fromType = fromType ?? this.fromType
			..id = id ?? this.id
			..idCard = idCard ?? this.idCard
			..imAccount = imAccount ?? this.imAccount
			..inviteCode = inviteCode ?? this.inviteCode
			..isPayment = isPayment ?? this.isPayment
			..level = level ?? this.level
			..mobile = mobile ?? this.mobile
			..nickname = nickname ?? this.nickname
			..pid = pid ?? this.pid
			..profiles = profiles ?? this.profiles
			..realName = realName ?? this.realName
			..score = score ?? this.score
			..sex = sex ?? this.sex
			..status = status ?? this.status
			..tradeStatus = tradeStatus ?? this.tradeStatus
			..type = type ?? this.type
			..uid = uid ?? this.uid;
	}

	@override
	String toString() {
		return jsonEncode(this);
	}
}