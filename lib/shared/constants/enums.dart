// ignore_for_file: constant_identifier_names

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/logic/sort_color/sort_color_cubit.dart';

enum DataStatus {
  idle,
  loading,
  success,
  failed;

  bool get isIdle => this == DataStatus.idle;

  bool get isLoading => this == DataStatus.loading;

  bool get isSuccess => this == DataStatus.success;

  bool get isFailed => this == DataStatus.failed;
}

enum HandleTypes { snackbar, dialog, customDialog }

enum WangYiCaptchaType {
  kLogin(1),
  kSms(2);

  final int code;
  const WangYiCaptchaType(this.code);
}

enum DepositType {
  bank,
  third,
}

enum WithdrawType {
  bank,
  third,
}

enum SnackBarType {
  warning,
  error,
  validation,
  success,
  info,
}

enum PaymentType {
  ERC20,
  TRC20,
}

enum TodaysTab {
  aShares(1),
  hkShares(2),
  usShares(3);

  const TodaysTab(this.code);
  final int code;

  static TodaysTab fromCode(int code) {
    try {
      return TodaysTab.values.firstWhere((e) => e.code == code);
    } on StateError {
      return TodaysTab.aShares; // 或者 throw RangeError('不存在的 code: $code');
    }
  }

  List<MarketType> get marketTypes {
    switch (this) {
      case TodaysTab.aShares:
        return [
          MarketType.shenzhen,
          MarketType.shanghai,
          MarketType.starMarket,
          MarketType.gem,
        ];
      case TodaysTab.hkShares:
        return [
          MarketType.gen,
          MarketType.main,
        ];
      case TodaysTab.usShares:
        return [
          MarketType.china,
          MarketType.star,
        ];
    }
  }

  String get market => switch (this) {
        TodaysTab.aShares => 'CN',
        TodaysTab.hkShares => 'HK',
        TodaysTab.usShares => 'US',
      };

  static TodaysTab byMarket(String market) {
    return switch (market) {
      'CN' || 'SZSE' => TodaysTab.aShares,
      'HK' || 'HKEX' => TodaysTab.hkShares,
      'US' => TodaysTab.usShares,
      _ => TodaysTab.aShares,
    };
  }
}

enum MarketType {
  // A Shares Markets
  shenzhen,
  shanghai,
  starMarket,
  gem,

  // HK Markets
  gen,
  main,

  // US Markets
  china,
  star;

  String get displayName {
    switch (this) {
      case MarketType.shenzhen:
        return 'Shenzhen';
      case MarketType.shanghai:
        return 'Shanghai';
      case MarketType.starMarket:
        return 'Star Market';
      case MarketType.gem:
        return 'GEM';
      case MarketType.gen:
        return 'GEN';
      case MarketType.main:
        return 'Main';
      case MarketType.china:
        return 'China';
      case MarketType.star:
        return 'Star';
    }
  }
}

enum AccountMarketType {
  currentPositions,

  ///entrust Deal
  tradeDetails,

  ///entrust
  orderDetails;

  String get socketEvent {
    return switch (this) {
      AccountMarketType.currentPositions => SocketEvents.position,
      AccountMarketType.tradeDetails => SocketEvents.entrustDeal,
      AccountMarketType.orderDetails => SocketEvents.entrust,
    };
  }

  String get translationKey => switch (this) {
        AccountMarketType.currentPositions => 'accountMarketTableTitle1',
        AccountMarketType.tradeDetails => 'accountMarketTableTitle2',
        AccountMarketType.orderDetails => 'accountMarketTableTitle3',
      };

  String translationKey2(String marketType) => switch (this) {
        AccountMarketType.currentPositions => 'accountMarketTableTitle1',
        AccountMarketType.tradeDetails => 'accountMarketTableTitle2',
        AccountMarketType.orderDetails => marketType == 'CN' ? 'accountMarketTableTitle3' : 'currentEntrustment',
      };
}

Map<String, String> stockIndexTranslation = {
  "道琼斯": ".DJI",
  "纳斯达克": ".IXIC",
  "标普500": ".INX",
};

enum CardTypeView {
  front,
  back,
}

enum CardType {
  id,
  passport;

  String get translationKey => switch (this) {
        CardType.id => 'id'.tr(),
        CardType.passport => 'passport'.tr(),
      };
}

enum ContractType {
  standard,
  bonus,
  experience;

  int get type => switch (this) {
        ContractType.standard => 1,
        ContractType.bonus => 2,
        ContractType.experience => 3,
      };

  String get translationKey => switch (this) {
        ContractType.standard => 'standard',
        ContractType.experience => 'experience',
        ContractType.bonus => 'bonus',
      };

  String title(InstrumentType mainContractType) => switch (mainContractType) {
        InstrumentType.stock => switch (this) {
            ContractType.standard => 'normalContract',
            ContractType.experience => 'experienceContract',
            ContractType.bonus => 'bonusContract',
          },
        InstrumentType.futures => switch (this) {
            ContractType.standard => 'futures.types.normalFutures',
            ContractType.experience => 'futures.types.experienceFutures',
            ContractType.bonus => 'futures.types.bonusFutures',
          },
      };

  int get value => switch (this) {
        ContractType.standard => 1,
        ContractType.experience => 2,
        ContractType.bonus => 1,
      };

  static ContractType fromValue(int value) {
    return switch (value) {
      1 => ContractType.standard,
      2 => ContractType.experience,
      3 => ContractType.bonus,
      _ => ContractType.standard,
    };
  }
}

Map<int, (String, String)> contractTypeTranslation = {
  1: ('perDay', 'day'),
  2: ('weekly', 'week'),
  3: ('perMonth', 'month'),
};

Map<int, String> contractActivityTypeTranslation = {
  1: 'perDay',
  7: 'weekly',
  30: 'perMonth',
};

Map<String, String> contractMarketTranslation = {
  "CN": 'cnStocks',
  "US": 'usStocks',
  "HK": 'hkStocks',
};

Map<int, String> marginCallTypeTranslation = {
  1: 'a_shares',
  2: 'hk_shares',
  3: 'us_shares',
};

enum ContractHistoryType {
  transaction,
  commission;

  String get translationKey => switch (this) {
        ContractHistoryType.transaction => 'dealHistory',
        ContractHistoryType.commission => 'orderHistory',
      };
}

/// Represents the direction of a trade - either buy or sell
enum TradeDirection {
  buy,
  sell;

  String get value => switch (this) {
        TradeDirection.buy => "1",
        TradeDirection.sell => "2",
      };

  String get translationKey => switch (this) {
        TradeDirection.buy => 'buy',
        TradeDirection.sell => 'sell2',
      };

  static TradeDirection fromValue(int value) {
    return switch (value) {
      1 => TradeDirection.buy,
      2 => TradeDirection.sell,
      _ => TradeDirection.buy,
    };
  }

  /// Gets the appropriate color for the trade direction based on market color settings
  /// Returns red/green based on market color preferences and trade direction
  static Color getColor(BuildContext context, {required TradeDirection tradeDirection}) {
    final sortColor = context.watch<SortColorCubit>().state.marketColor;
    return switch (tradeDirection) {
      TradeDirection.buy => switch (sortColor) {
          MarketColor.redUpGreenDown => context.colorTheme.stockRed,
          MarketColor.greenUpRedDown => context.colorTheme.stockGreen,
        },
      TradeDirection.sell => switch (sortColor) {
          MarketColor.redUpGreenDown => context.colorTheme.stockGreen,
          MarketColor.greenUpRedDown => context.colorTheme.stockRed,
        },
    };
  }
}

/// Represents different types of price orders that can be placed
/// - market: Market price order executed at current market price
/// - limit: Limit price order executed at user specified price
enum PriceType {
  market,
  limit;

  /// Returns the numeric value associated with the price type
  /// market = 1, limit = 2
  int get value => switch (this) {
        PriceType.market => 1,
        PriceType.limit => 2,
      };

  String get tr => switch (this) {
        PriceType.market => 'marketOrder'.tr(),
        PriceType.limit => 'limitOrder'.tr(),
      };
}

enum SecurityType {
  stocks(1, 'Stocks (HK, US, SH, SZ)'),
  indexx(2, 'Index (HK, US, SH, SZ)'),
  others(3, 'Others (HK, US, SH, SZ)'),
  cnFTrade(4, 'cnFTrade'),
  warrants(11, 'Warrants (HK)'),
  cbbc(12, 'CBBC (HK)');

  final int code;
  final String description;

  const SecurityType(this.code, this.description);

  static SecurityType fromCode(int code) {
    return SecurityType.values.firstWhere(
      (type) => type.code == code,
      orElse: () => SecurityType.stocks,
    );
  }

  static int toCode(SecurityType type) {
    return type.code;
  }

  String get tr {
    return switch (this) {
      SecurityType.stocks => 'stocks',
      SecurityType.indexx => 'stockIndex',
      _ => '',
    };
  }
}

enum TradeType {
  long,
  short;

  int get value => switch (this) {
        TradeType.long => 1,
        TradeType.short => 2,
      };

  static TradeType fromValue(int value) {
    return switch (value) {
      1 => TradeType.long,
      2 => TradeType.short,
      _ => TradeType.long,
    };
  }

  String get translationKey => switch (this) {
        TradeType.long => 'openLongSymbol',
        TradeType.short => 'openShortSymbol',
      };

  static Color getColor(BuildContext context, {required TradeType tradeType}) {
    final sortColor = context.watch<SortColorCubit>().state.marketColor;
    return switch (tradeType) {
      TradeType.long => switch (sortColor) {
          MarketColor.redUpGreenDown => context.colorTheme.stockRed,
          MarketColor.greenUpRedDown => context.colorTheme.stockGreen,
        },
      TradeType.short => switch (sortColor) {
          MarketColor.redUpGreenDown => context.colorTheme.stockGreen,
          MarketColor.greenUpRedDown => context.colorTheme.stockRed,
        },
    };
  }
}

enum OrderFraction {
  oneFourth(0.25, "1/4"),
  oneThird(0.33333333, "1/3"),
  oneHalf(0.5, "1/2"),
  full(1.0, "全仓", "full");

  final double fraction;
  final String label;
  final String? translationKey;

  const OrderFraction(this.fraction, this.label, [this.translationKey]);
}

enum MainMarketType {
  cnShares,
  hkShares,
  usShares;

  String get type {
    return switch (this) {
      cnShares => 'CN',
      hkShares => 'HK',
      usShares => 'US',
    };
  }

  List<MarketType> get marketTypes {
    switch (this) {
      case MainMarketType.cnShares:
        return [
          MarketType.shenzhen,
          MarketType.shanghai,
          MarketType.starMarket,
          MarketType.gem,
        ];
      case MainMarketType.hkShares:
        return [
          MarketType.gen,
          MarketType.main,
        ];
      case MainMarketType.usShares:
        return [
          MarketType.china,
          MarketType.star,
        ];
    }
  }

  static MarketSymbol getMarketSymbol(MainMarketType mainMarketType) {
    return switch (mainMarketType) {
      MainMarketType.cnShares => MarketSymbol.CN,
      MainMarketType.hkShares => MarketSymbol.HK,
      MainMarketType.usShares => MarketSymbol.US,
    };
  }
}

enum MarketSymbol {
  CN,
  HK,
  US;

  String get displayName => switch (this) {
        MarketSymbol.CN => 'a_shares',
        MarketSymbol.HK => 'hk_shares',
        MarketSymbol.US => 'us_shares',
      };

  String get marketSymbol => switch (this) {
        MarketSymbol.CN => 'SZSE',
        MarketSymbol.HK => 'HKEX',
        MarketSymbol.US => 'US',
      };

  static MarketSymbol fromMarketType(String marketType) {
    return switch (marketType) {
      'CN' => MarketSymbol.CN,
      'HK' => MarketSymbol.HK,
      'US' => MarketSymbol.US,
      _ => MarketSymbol.CN,
    };
  }

  static MainMarketType getMainMarketType(String marketType) {
    return switch (marketType) {
      'CN' => MainMarketType.cnShares,
      'HK' => MainMarketType.hkShares,
      'US' => MainMarketType.usShares,
      _ => MainMarketType.cnShares,
    };
  }

  static String? getMarketType(String? marketType) {
    return switch (marketType) {
      'SZSE' || 'SSE' => 'CN',
      'HKEX' => 'HK',
      'US' => 'US',
      _ => null,
    };
  }
}

extension MarketSymbolExtension on MarketSymbol {
  String get market {
    return switch (this) {
      MarketSymbol.CN => 'SZSE',
      MarketSymbol.HK => 'HKEX',
      MarketSymbol.US => 'US',
    };
  }

  String get symbol {
    return switch (this) {
      MarketSymbol.CN => '000001',
      MarketSymbol.HK => '00001',
      MarketSymbol.US => 'AAPL',
    };
  }

  int get securityType {
    return switch (this) {
      MarketSymbol.CN || MarketSymbol.US || MarketSymbol.HK => 1,
    };
  }
}

enum SortType {
  DESC,
  ASC;

  String get toParameters {
    switch (this) {
      case DESC:
        return 'DESC';
      case ASC:
        return 'ASC';
    }
  }
}

enum MarketColor {
  redUpGreenDown,
  greenUpRedDown,
}

enum NotificationType {
  warning,
  notice,
  system,
}

enum StockWidgetCount {
  one(1),
  five(5);

  final int value;

  const StockWidgetCount(this.value);
}

enum TradeTabType {
  Trading,
  Quotes,
}

enum ContractAction {
  replenish,
  renew,
  marginExpand;

  int get value => switch (this) {
        ContractAction.marginExpand => 1,
        ContractAction.replenish => 2,
        ContractAction.renew => 3,
      };
}

enum EntrustStatus {
  commision(0),
  cancel(1),
  success(2),
  expire(3);

  static Map<int, EntrustStatus> fromValue = {
    0: commision,
    1: cancel,
    2: success,
    3: expire,
  };

  static EntrustStatus fromValueByValue(int value) => fromValue[value] ?? cancel;

  const EntrustStatus(this.value);

  final int value;

  String get label => switch (this) {
        EntrustStatus.commision => 'commision',
        EntrustStatus.cancel => 'revocation',
        EntrustStatus.success => 'successCommission',
        EntrustStatus.expire => 'autoCancel',
      };
}

extension EntrustStatusExtension on EntrustStatus {
  Color color(BuildContext context) => switch (this) {
        EntrustStatus.commision => context.theme.primaryColor,
        EntrustStatus.cancel => context.colorTheme.border,
        EntrustStatus.success => context.colorTheme.stockGreen,
        EntrustStatus.expire => context.colorTheme.border,
      };

  String get label => switch (this) {
        EntrustStatus.commision => 'commision',
        EntrustStatus.cancel => 'revocation',
        EntrustStatus.success => 'successCommission',
        EntrustStatus.expire => 'autoCancel',
      };
}

enum BuyType { long, short, none }

enum SellType { long, short, none }

enum TradeTypeOption {
  openLong(1, 'openLong', 'openLongSymbol'),
  openShort(2, 'openShort', 'openShortSymbol');

  final int value;
  final String label;
  final String shortLabel;

  const TradeTypeOption(this.value, this.label, this.shortLabel);

  bool get isRaise => this == TradeTypeOption.openLong;

  static TradeTypeOption fromValue(int? value) {
    return TradeTypeOption.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TradeTypeOption.openLong,
    );
  }

  String get text => label.tr();
  String get shortText => shortLabel.tr();

  Color color(BuildContext context) => switch (this) {
        TradeTypeOption.openLong => context.upColor,
        TradeTypeOption.openShort => context.downColor,
      };
}

enum LoginMode {
  account,
  mobile;

  String get text => switch (this) {
        LoginMode.account => 'account',
        LoginMode.mobile => 'mobile',
      };
}

enum TabType {
  tabMarket(),
  tabCapital(),
  news(),
  companyProfile();

  const TabType();

  static List<TabType> get indexTradingTabs => [
        TabType.tabMarket,
        TabType.news,
      ];
}

enum AccountType {
  spot,
  contract,
}

enum TransferType {
  deposit,
  withdraw;
}

enum PasswordChangeType { accountVerification, smsVerification }

enum OtpType {
  login,
  register,
  updatePassword,
  bindBankCard,
  updateMobile;

  String get text => switch (this) {
        OtpType.login => 'login',
        OtpType.register => 'register',
        OtpType.updatePassword => 'updatePassword',
        OtpType.bindBankCard => 'bindCard',
        OtpType.updateMobile => 'updateMobile',
      };
}

enum PasswordType {
  account, // 账户密码修改
  withdrawal; // 提现密码修改

  int get value => switch (this) {
        PasswordType.account => 1,
        PasswordType.withdrawal => 2,
      };
}

enum PasswordModifyType {
  account,
  financial;

  String get title {
    switch (this) {
      case PasswordModifyType.account:
        return 'password_account';
      case PasswordModifyType.financial:
        return 'password_financial';
    }
  }
}

enum ContractAuditStatus {
  pending,
  success,
  failure;

  String get text => switch (this) {
        ContractAuditStatus.pending => 'reviewPending',
        ContractAuditStatus.success => 'reviewSuccessful',
        ContractAuditStatus.failure => 'reviewFailed',
      };

  Color color(BuildContext context) => switch (this) {
        ContractAuditStatus.pending => context.colorTheme.pending,
        ContractAuditStatus.success => context.colorTheme.stockGreen,
        ContractAuditStatus.failure => context.colorTheme.stockRed,
      };
}

/// 交易对详情页面顶部的 Tab 类型（行情 / 交易）
///
/// Symbol tab type for exchange detail page (Quote / Trade)
enum SymbolTabType {
  /// 行情：K线图、价格走势、盘口信息
  /// Quote: K-line chart, price trend, order book, etc.
  quote,

  /// 交易：买入卖出、挂单、下单面板
  /// Trade: Buy/sell actions, order panel, place order
  trade,
}

/// 交易账户类型枚举
///
/// Defines trading account types: Contract (合约) and Spot (现货).
enum TradingAccountType {
  /// 合约账户（Contract Account）
  Contract("contractAccount"),

  /// 现货账户（Spot Account）
  Spot("spotAccount");

  /// 对应的账户键名（用于接口参数或本地映射）
  /// The key name used for API or local mapping.
  final String nameKey;

  const TradingAccountType(this.nameKey);
}

/// 金融工具类型（Instrument Type）
/// 表示交易中支持的标的类型，例如股票类或期货类
///
/// Instrument Type used to distinguish different tradable instruments,
/// such as stocks and futures.
enum InstrumentType {
  /// 股票类（Stock）
  /// 如：A股、美股、ETF、指数等
  /// Stock-like products, including A-shares, US stocks, ETFs, indexes, etc.
  stock,

  /// 期货类（Futures）
  /// 如：交割合约、永续合约等衍生品
  /// Futures-like instruments, including delivery contracts, perpetual contracts, etc.
  futures;

  /// 多语言翻译 key，用于 UI 文案显示
  /// Translation key used for UI internationalization
  String get translationKey => switch (this) {
        InstrumentType.stock => 'applyContract', // 请根据上下文替换为更精准的 key
        InstrumentType.futures => 'futures.applyFuturesFunding',
      };

  /// 枚举对应的整数值，用于后端参数传递或存储
  /// Integer value mapped to each instrument type (used in API or DB)
  int get value => switch (this) {
        InstrumentType.stock => 1,
        InstrumentType.futures => 2,
      };
}

enum MarketSectionTab {
  stock,
  stockIndex,
  futures,
  watchList;
}
